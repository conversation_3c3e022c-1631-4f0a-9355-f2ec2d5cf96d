# نظام نقاط البيع (POS System)

نظام شامل لإدارة المبيعات والمخزون باستخدام Python Flask مع واجهة عربية سهلة الاستخدام.

## 🌟 المميزات الرئيسية

### ✅ نظام المصادقة والصلاحيات
- تسجيل دخول آمن للمستخدمين
- إدارة الأدوار (مدير، كاشير، محاسب)
- تتبع آخر دخول للمستخدمين

### 📊 لوحة التحكم الرئيسية
- إحصائيات المبيعات اليومية والشهرية
- عدد الفواتير والمنتجات
- تنبيهات المخزون المنخفض
- المنتجات الأكثر مبيعاً

### 🛒 نقطة البيع (POS)
- واجهة سهلة وسريعة للبيع
- دعم البحث بالاسم والباركود
- تصفية المنتجات حسب التصنيف
- حساب الضرائب والخصومات تلقائياً
- دعم طرق دفع متعددة (نقدي، بطاقة، آجل)

### 📦 إدارة المنتجات والمخزون
- إضافة وتعديل المنتجات
- تتبع المخزون الحالي
- تنبيهات الكمية المنخفضة
- دعم الباركود والصور
- تصنيف المنتجات

### 🧾 إدارة الفواتير
- عرض وطباعة الفواتير
- تصفية حسب التاريخ والعميل
- تتبع حالة الدفع
- إمكانية الإلغاء والتعديل

### 👥 إدارة العملاء
- قاعدة بيانات العملاء
- تتبع المشتريات السابقة
- دعم المبيعات الآجلة

### 📈 التقارير الشاملة
- تقارير المبيعات حسب الفترة
- تقارير الأرباح والخسائر
- تقارير المنتجات الأكثر مبيعاً
- إمكانية التصدير

### 🖨️ دعم الطباعة والباركود
- طباعة الفواتير الحرارية
- توليد وطباعة الباركود
- تخصيص تصميم الفاتورة

## 🛠️ التقنيات المستخدمة

- **Backend**: Python Flask
- **Database**: SQLite (قابل للتغيير إلى PostgreSQL/MySQL)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5 (RTL)
- **Authentication**: Flask-Login
- **PDF Generation**: ReportLab, WeasyPrint
- **Barcode**: python-barcode, qrcode
- **Icons**: Font Awesome
- **Fonts**: Google Fonts (Cairo)

## 📋 متطلبات النظام

- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- متصفح ويب حديث

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd pos-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
قم بتعديل ملف `.env` حسب احتياجاتك:
```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///pos_system.db
COMPANY_NAME=اسم شركتك
COMPANY_ADDRESS=عنوان الشركة
# ... باقي الإعدادات
```

### 5. إنشاء قاعدة البيانات والبيانات التجريبية

#### الطريقة الأولى: تشغيل سريع مع البيانات التجريبية
```bash
python setup_demo.py
```

#### الطريقة الثانية: تشغيل أساسي
```bash
python run.py
```

#### الطريقة الثالثة: تشغيل مباشر
```bash
python app.py
```

### 6. الوصول للنظام
سيعمل النظام على: `http://localhost:5000`

**ملاحظة:** يُنصح باستخدام `setup_demo.py` للحصول على بيانات تجريبية شاملة تساعد في فهم النظام.

## 👤 بيانات تسجيل الدخول الافتراضية

### المدير
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### الكاشير
- **اسم المستخدم**: cashier
- **كلمة المرور**: cashier123

## 📱 كيفية الاستخدام

### 1. تسجيل الدخول
- افتح المتصفح وانتقل إلى `http://localhost:5000`
- استخدم بيانات تسجيل الدخول أعلاه

### 2. إضافة منتجات جديدة
- انتقل إلى "المنتجات" → "إضافة منتج جديد"
- املأ البيانات المطلوبة
- احفظ المنتج

### 3. إجراء عملية بيع
- انتقل إلى "نقطة البيع"
- اختر المنتجات أو استخدم البحث/الباركود
- أضف المنتجات للسلة
- اختر طريقة الدفع
- أتمم البيع

### 4. عرض التقارير
- انتقل إلى "التقارير"
- اختر نوع التقرير والفترة الزمنية
- عرض أو تصدير التقرير

## 🔧 التخصيص والإعدادات

### تغيير معلومات الشركة
- انتقل إلى "الإعدادات" (للمدير فقط)
- عدّل معلومات الشركة
- احفظ التغييرات

### إضافة مستخدمين جدد
يمكن للمدير إضافة مستخدمين جدد من خلال لوحة الإعدادات.

### تخصيص الضرائب
- عدّل معدل الضريبة في الإعدادات
- سيتم تطبيقها تلقائياً على جميع المبيعات

## 📊 هيكل قاعدة البيانات

- **Users**: المستخدمون وصلاحياتهم
- **Categories**: تصنيفات المنتجات
- **Products**: المنتجات ومعلوماتها
- **Customers**: بيانات العملاء
- **Invoices**: الفواتير الرئيسية
- **InvoiceItems**: عناصر كل فاتورة
- **StockMovements**: حركات المخزون
- **SystemSettings**: إعدادات النظام

## 🔒 الأمان

- تشفير كلمات المرور
- جلسات آمنة
- حماية من CSRF
- تحقق من الصلاحيات

## 🐛 استكشاف الأخطاء

### مشكلة في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm pos_system.db
python seed_data.py
```

### مشكلة في المكتبات
```bash
# إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall
```

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

---

**تم تطوير هذا النظام بـ ❤️ لخدمة المتاجر والشركات الصغيرة والمتوسطة**
