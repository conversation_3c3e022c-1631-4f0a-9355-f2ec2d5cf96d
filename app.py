from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
from sqlalchemy import func, desc
import os
import json
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'qatar-pos-secret-key-2025')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///qatar_pos_system.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعدادات قطر
app.config['COUNTRY'] = 'Qatar'
app.config['CURRENCY'] = 'QAR'
app.config['CURRENCY_SYMBOL'] = 'ر.ق'
app.config['CURRENCY_FORMAT'] = 'ر.ق {:.2f}'
app.config['VAT_ENABLED'] = False  # لا توجد ضريبة حالياً في قطر
app.config['VAT_RATE'] = 0.0
app.config['PHONE_PREFIX'] = '+974'
app.config['DATE_FORMAT'] = '%d/%m/%Y'
app.config['DATETIME_FORMAT'] = '%d/%m/%Y %H:%M'

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='cashier')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    products = db.relationship('Product', backref='category', lazy=True)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    barcode = db.Column(db.String(50), unique=True)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    purchase_price = db.Column(db.Float, nullable=False, default=0.0)
    selling_price = db.Column(db.Float, nullable=False)
    stock_quantity = db.Column(db.Integer, nullable=False, default=0)
    min_stock_level = db.Column(db.Integer, default=5)
    image_path = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def is_low_stock(self):
        return self.stock_quantity <= self.min_stock_level

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    credit_balance = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    invoices = db.relationship('Invoice', backref='customer', lazy=True)

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subtotal = db.Column(db.Float, nullable=False, default=0.0)
    tax_amount = db.Column(db.Float, nullable=False, default=0.0)
    discount_amount = db.Column(db.Float, nullable=False, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)
    payment_status = db.Column(db.String(20), default='paid')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref='invoices')
    items = db.relationship('InvoiceItem', backref='invoice', lazy=True, cascade='all, delete-orphan')

class InvoiceItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    product = db.relationship('Product', backref='invoice_items')

class StockMovement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    movement_type = db.Column(db.String(20), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    reference_type = db.Column(db.String(20))
    reference_id = db.Column(db.Integer)
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    product = db.relationship('Product', backref='stock_movements')
    user = db.relationship('User', backref='stock_movements')

class SystemSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات الأساسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()

            next_page = request.args.get('next')
            flash(f'مرحباً {user.full_name}!', 'success')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('auth/login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    today = datetime.now().date()
    today_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        func.date(Invoice.created_at) == today,
        Invoice.payment_status == 'paid'
    ).scalar() or 0

    today_invoices = Invoice.query.filter(
        func.date(Invoice.created_at) == today
    ).count()

    month_start = today.replace(day=1)
    month_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.created_at >= month_start,
        Invoice.payment_status == 'paid'
    ).scalar() or 0

    low_stock_products = Product.query.filter(
        Product.stock_quantity <= Product.min_stock_level,
        Product.is_active == True
    ).all()

    recent_invoices = Invoice.query.order_by(desc(Invoice.created_at)).limit(5).all()

    thirty_days_ago = datetime.now() - timedelta(days=30)
    top_products = db.session.query(
        Product.name,
        func.sum(InvoiceItem.quantity).label('total_sold')
    ).join(InvoiceItem).join(Invoice).filter(
        Invoice.created_at >= thirty_days_ago,
        Invoice.payment_status == 'paid'
    ).group_by(Product.id).order_by(desc('total_sold')).limit(5).all()

    return render_template('dashboard.html',
                         today_sales=today_sales,
                         today_invoices=today_invoices,
                         month_sales=month_sales,
                         low_stock_products=low_stock_products,
                         recent_invoices=recent_invoices,
                         top_products=top_products)

@app.route('/pos')
@login_required
def pos():
    categories = Category.query.filter_by(is_active=True).all()
    products = Product.query.filter_by(is_active=True).all()
    customers = Customer.query.filter_by(is_active=True).all()

    return render_template('pos/index.html',
                         categories=categories,
                         products=products,
                         customers=customers)

@app.route('/api/search_product')
@login_required
def search_product():
    query = request.args.get('q', '')
    barcode = request.args.get('barcode', '')

    if barcode:
        product = Product.query.filter_by(barcode=barcode, is_active=True).first()
        if product:
            return jsonify({
                'success': True,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'price': product.selling_price,
                    'stock': product.stock_quantity,
                    'barcode': product.barcode
                }
            })

    if query:
        products = Product.query.filter(
            Product.name.contains(query),
            Product.is_active == True
        ).limit(10).all()

        return jsonify({
            'success': True,
            'products': [{
                'id': p.id,
                'name': p.name,
                'price': p.selling_price,
                'stock': p.stock_quantity,
                'barcode': p.barcode
            } for p in products]
        })

    return jsonify({'success': False, 'message': 'لم يتم العثور على منتجات'})

@app.route('/api/create_invoice', methods=['POST'])
@login_required
def create_invoice():
    try:
        data = request.get_json()

        last_invoice = Invoice.query.order_by(desc(Invoice.id)).first()
        invoice_number = f"INV-{(last_invoice.id + 1) if last_invoice else 1:06d}"

        invoice = Invoice(
            invoice_number=invoice_number,
            customer_id=data.get('customer_id'),
            user_id=current_user.id,
            subtotal=data['subtotal'],
            tax_amount=data['tax_amount'],
            discount_amount=data.get('discount_amount', 0),
            total_amount=data['total_amount'],
            payment_method=data['payment_method'],
            notes=data.get('notes', '')
        )

        db.session.add(invoice)
        db.session.flush()

        for item in data['items']:
            invoice_item = InvoiceItem(
                invoice_id=invoice.id,
                product_id=item['product_id'],
                quantity=item['quantity'],
                unit_price=item['unit_price'],
                total_price=item['total_price']
            )
            db.session.add(invoice_item)

            product = Product.query.get(item['product_id'])
            if product:
                product.stock_quantity -= item['quantity']

                stock_movement = StockMovement(
                    product_id=product.id,
                    movement_type='out',
                    quantity=item['quantity'],
                    reference_type='invoice',
                    reference_id=invoice.id,
                    user_id=current_user.id,
                    notes=f'بيع - فاتورة رقم {invoice_number}'
                )
                db.session.add(stock_movement)

        db.session.commit()

        return jsonify({
            'success': True,
            'invoice_id': invoice.id,
            'invoice_number': invoice_number,
            'message': 'تم إنشاء الفاتورة بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })

@app.route('/products')
@login_required
def products():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)

    query = Product.query

    if search:
        query = query.filter(Product.name.contains(search))

    if category_id:
        query = query.filter_by(category_id=category_id)

    products = query.paginate(
        page=page, per_page=20, error_out=False
    )

    categories = Category.query.filter_by(is_active=True).all()

    return render_template('products/index.html',
                         products=products,
                         categories=categories,
                         search=search,
                         selected_category=category_id)

@app.route('/invoices')
@login_required
def invoices():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Invoice.query

    if search:
        query = query.join(Customer).filter(
            db.or_(
                Invoice.invoice_number.contains(search),
                Customer.name.contains(search)
            )
        )

    invoices = query.order_by(desc(Invoice.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('invoices/index.html', invoices=invoices, search=search)

@app.route('/invoice/<int:invoice_id>')
@login_required
def view_invoice(invoice_id):
    invoice = Invoice.query.get_or_404(invoice_id)
    return render_template('invoices/view.html', invoice=invoice)

@app.route('/invoice/<int:invoice_id>/print')
@login_required
def print_invoice(invoice_id):
    invoice = Invoice.query.get_or_404(invoice_id)
    return render_template('invoices/print.html', invoice=invoice)

@app.route('/customers')
@login_required
def customers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Customer.query

    if search:
        query = query.filter(
            db.or_(
                Customer.name.contains(search),
                Customer.phone.contains(search)
            )
        )

    customers = query.paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('customers/index.html', customers=customers, search=search)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if request.method == 'POST':
        try:
            customer = Customer(
                name=request.form['name'],
                phone=request.form.get('phone'),
                email=request.form.get('email'),
                address=request.form.get('address'),
                credit_balance=float(request.form.get('credit_balance', 0)),
                is_active=bool(request.form.get('is_active'))
            )

            db.session.add(customer)
            db.session.commit()

            flash('تم إضافة العميل بنجاح', 'success')
            return redirect(url_for('customers'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('customers/add.html')

@app.route('/customers/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        try:
            customer.name = request.form['name']
            customer.phone = request.form.get('phone')
            customer.email = request.form.get('email')
            customer.address = request.form.get('address')
            customer.credit_balance = float(request.form.get('credit_balance', 0))
            customer.is_active = bool(request.form.get('is_active'))

            db.session.commit()
            flash('تم تحديث بيانات العميل بنجاح', 'success')
            return redirect(url_for('customers'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('customers/edit.html', customer=customer)

@app.route('/reports')
@login_required
def reports():
    return render_template('reports/index.html')

@app.route('/reports/sales')
@login_required
def sales_report():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    payment_method = request.args.get('payment_method')

    query = Invoice.query.filter_by(payment_status='paid')

    if start_date:
        query = query.filter(Invoice.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))

    if end_date:
        query = query.filter(Invoice.created_at <= datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S'))

    if payment_method:
        query = query.filter_by(payment_method=payment_method)

    invoices = query.order_by(desc(Invoice.created_at)).all()

    total_sales = sum(invoice.total_amount for invoice in invoices)
    total_invoices = len(invoices)

    return render_template('reports/sales.html',
                         invoices=invoices,
                         total_sales=total_sales,
                         total_invoices=total_invoices,
                         start_date=start_date,
                         end_date=end_date)

@app.route('/reports/products')
@login_required
def products_report():
    return render_template('reports/products.html')

@app.route('/reports/customers')
@login_required
def customers_report():
    return render_template('reports/customers.html')

@app.route('/reports/financial')
@login_required
def financial_report():
    return render_template('reports/financial.html')

@app.route('/settings')
@login_required
def settings():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    return render_template('settings/index.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")

    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")

    app.run(debug=True, host='0.0.0.0', port=5000)
