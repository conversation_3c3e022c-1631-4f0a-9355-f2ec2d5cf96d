from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
from sqlalchemy import func, desc
import os
import json
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'qatar-pos-secret-key-2025')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///qatar_pos_system.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعدادات قطر الشاملة
QATAR_CONFIG = {
    # 1. العملة
    'COUNTRY': 'Qatar',
    'COUNTRY_AR': 'قطر',
    'CURRENCY': 'QAR',
    'CURRENCY_SYMBOL': 'ر.ق',
    'CURRENCY_FORMAT': 'ر.ق {:.2f}',

    # 2. اللغة (ثنائي اللغة)
    'DEFAULT_LANGUAGE': 'ar',
    'SUPPORTED_LANGUAGES': ['ar', 'en'],
    'LANGUAGE_NAMES': {'ar': 'العربية', 'en': 'English'},

    # 3. الضريبة
    'VAT_ENABLED': False,  # لا توجد ضريبة حالياً في قطر
    'VAT_RATE': 0.0,
    'VAT_CONFIGURABLE': True,  # يمكن تفعيلها لاحقاً

    # 4. التاريخ والتقويم
    'DATE_FORMAT': '%d/%m/%Y',
    'DATETIME_FORMAT': '%d/%m/%Y %H:%M',
    'ALTERNATIVE_DATE_FORMAT': '%Y-%m-%d',
    'CALENDAR_TYPE': 'gregorian',

    # 5. معلومات المتجر والفواتير
    'COMPANY_NAME': 'لمسة الخليج',
    'COMPANY_NAME_EN': 'Gulf Touch',
    'CR_NUMBER': '123456',  # رقم السجل التجاري
    'ADDRESS_AR': 'الدوحة، دولة قطر',
    'ADDRESS_EN': 'Doha, State of Qatar',
    'NATIONAL_ADDRESS': 'المنطقة 1، الشارع 123، المبنى 456، الدوحة، قطر',
    'LOGO_PATH': '/static/images/logo.png',

    # 6. المواقع (المدن القطرية)
    'CITIES': [
        'الدوحة', 'الوكرة', 'الخور', 'الريان', 'أم صلال',
        'الشمال', 'الضعاين', 'الشحانية', 'دخان', 'مسيعيد'
    ],
    'DEFAULT_CITY': 'الدوحة',

    # 7. أرقام الهاتف القطرية
    'PHONE_PREFIX': '+974',
    'PHONE_PATTERNS': [
        r'^\+974\s?[3-7]\d{7}$',  # +974 55123456
        r'^[3-7]\d{7}$'           # 55123456
    ],
    'PHONE_EXAMPLE': '+974 55123456',

    # معلومات التواصل
    'PHONE': '+974 55123456',
    'WHATSAPP': '+974 77123456',
    'EMAIL': '<EMAIL>',
    'WEBSITE': 'www.gulftouch.qa',

    # إعدادات الفاتورة
    'INVOICE_PREFIX': 'QA-INV-',
    'RECEIPT_PREFIX': 'QA-REC-',
    'INVOICE_FOOTER_AR': 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
    'INVOICE_FOOTER_EN': 'Thank you for your visit - We look forward to serving you again'
}

# تطبيق الإعدادات على Flask
for key, value in QATAR_CONFIG.items():
    app.config[key] = value

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='cashier')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    products = db.relationship('Product', backref='category', lazy=True)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    barcode = db.Column(db.String(50), unique=True)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    purchase_price = db.Column(db.Float, nullable=False, default=0.0)
    selling_price = db.Column(db.Float, nullable=False)
    stock_quantity = db.Column(db.Integer, nullable=False, default=0)
    min_stock_level = db.Column(db.Integer, default=5)
    image_path = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def is_low_stock(self):
        return self.stock_quantity <= self.min_stock_level

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))  # الاسم بالإنجليزية
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    city = db.Column(db.String(50), default='الدوحة')  # المدينة القطرية
    qid = db.Column(db.String(20))  # رقم الهوية القطرية
    nationality = db.Column(db.String(50), default='قطري')  # الجنسية
    credit_balance = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    invoices = db.relationship('Invoice', backref='customer', lazy=True)

    def format_phone(self):
        """تنسيق رقم الهاتف القطري"""
        if self.phone and not self.phone.startswith('+974'):
            import re
            clean_phone = re.sub(r'[^\d]', '', self.phone)
            if len(clean_phone) == 8:
                return f"+974 {clean_phone}"
        return self.phone

    def validate_qid(self):
        """التحقق من صحة رقم الهوية القطرية"""
        if self.qid:
            import re
            return bool(re.match(r'^\d{11}$', self.qid))
        return True

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subtotal = db.Column(db.Float, nullable=False, default=0.0)
    tax_amount = db.Column(db.Float, nullable=False, default=0.0)
    discount_amount = db.Column(db.Float, nullable=False, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)
    payment_status = db.Column(db.String(20), default='paid')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref='invoices')
    items = db.relationship('InvoiceItem', backref='invoice', lazy=True, cascade='all, delete-orphan')

class InvoiceItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    product = db.relationship('Product', backref='invoice_items')

class StockMovement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    movement_type = db.Column(db.String(20), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    reference_type = db.Column(db.String(20))
    reference_id = db.Column(db.Integer)
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    product = db.relationship('Product', backref='stock_movements')
    user = db.relationship('User', backref='stock_movements')

class SystemSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# دوال مساعدة للمتطلبات القطرية
def format_currency(amount):
    """تنسيق العملة القطرية"""
    return QATAR_CONFIG['CURRENCY_FORMAT'].format(amount)

def format_phone_qatar(phone):
    """تنسيق رقم الهاتف القطري"""
    if not phone:
        return phone

    import re
    # إزالة جميع الرموز غير الرقمية
    clean_phone = re.sub(r'[^\d]', '', phone)

    # إذا كان الرقم يبدأ بـ 974، إزالته
    if clean_phone.startswith('974'):
        clean_phone = clean_phone[3:]

    # التحقق من صحة الرقم القطري (8 أرقام تبدأ بـ 3-7)
    if len(clean_phone) == 8 and clean_phone[0] in '34567':
        return f"+974 {clean_phone}"

    return phone

def validate_qatar_phone(phone):
    """التحقق من صحة رقم الهاتف القطري"""
    if not phone:
        return True

    import re
    for pattern in QATAR_CONFIG['PHONE_PATTERNS']:
        if re.match(pattern, phone):
            return True
    return False

def validate_qatar_qid(qid):
    """التحقق من صحة رقم الهوية القطرية"""
    if not qid:
        return True

    import re
    # رقم الهوية القطرية يتكون من 11 رقم
    return bool(re.match(r'^\d{11}$', qid))

def generate_invoice_number():
    """توليد رقم فاتورة قطري"""
    last_invoice = Invoice.query.order_by(desc(Invoice.id)).first()
    next_number = (last_invoice.id + 1) if last_invoice else 1
    return f"{QATAR_CONFIG['INVOICE_PREFIX']}{next_number:06d}"

def get_user_language():
    """الحصول على لغة المستخدم"""
    return session.get('language', QATAR_CONFIG['DEFAULT_LANGUAGE'])

def set_user_language(language):
    """تعيين لغة المستخدم"""
    if language in QATAR_CONFIG['SUPPORTED_LANGUAGES']:
        session['language'] = language
        return True
    return False

# إضافة الدوال للقوالب
@app.template_filter('currency')
def currency_filter(amount):
    return format_currency(amount)

@app.template_filter('phone_qatar')
def phone_qatar_filter(phone):
    return format_phone_qatar(phone)

@app.template_global()
def get_qatar_config():
    return QATAR_CONFIG

@app.template_global()
def get_current_language():
    return get_user_language()

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات الأساسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        city = request.form.get('city', QATAR_CONFIG['DEFAULT_CITY'])
        language = request.form.get('language', QATAR_CONFIG['DEFAULT_LANGUAGE'])

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)
            user.last_login = datetime.utcnow()

            # حفظ تفضيلات المستخدم
            session['user_city'] = city
            session['language'] = language

            db.session.commit()

            next_page = request.args.get('next')
            welcome_msg = f'مرحباً {user.full_name}!' if language == 'ar' else f'Welcome {user.full_name}!'
            flash(welcome_msg, 'success')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            error_msg = 'اسم المستخدم أو كلمة المرور غير صحيحة' if get_user_language() == 'ar' else 'Invalid username or password'
            flash(error_msg, 'error')

    return render_template('auth/login.html', qatar_config=QATAR_CONFIG)

@app.route('/set_language/<language>')
def set_language(language):
    """تبديل اللغة"""
    if set_user_language(language):
        flash('تم تغيير اللغة بنجاح' if language == 'ar' else 'Language changed successfully', 'success')
    else:
        flash('لغة غير مدعومة' if get_user_language() == 'ar' else 'Unsupported language', 'error')

    return redirect(request.referrer or url_for('index'))

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    today = datetime.now().date()
    today_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        func.date(Invoice.created_at) == today,
        Invoice.payment_status == 'paid'
    ).scalar() or 0

    today_invoices = Invoice.query.filter(
        func.date(Invoice.created_at) == today
    ).count()

    month_start = today.replace(day=1)
    month_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.created_at >= month_start,
        Invoice.payment_status == 'paid'
    ).scalar() or 0

    low_stock_products = Product.query.filter(
        Product.stock_quantity <= Product.min_stock_level,
        Product.is_active == True
    ).all()

    recent_invoices = Invoice.query.order_by(desc(Invoice.created_at)).limit(5).all()

    thirty_days_ago = datetime.now() - timedelta(days=30)
    top_products = db.session.query(
        Product.name,
        func.sum(InvoiceItem.quantity).label('total_sold')
    ).join(InvoiceItem).join(Invoice).filter(
        Invoice.created_at >= thirty_days_ago,
        Invoice.payment_status == 'paid'
    ).group_by(Product.id).order_by(desc('total_sold')).limit(5).all()

    return render_template('dashboard.html',
                         today_sales=today_sales,
                         today_invoices=today_invoices,
                         month_sales=month_sales,
                         low_stock_products=low_stock_products,
                         recent_invoices=recent_invoices,
                         top_products=top_products)

@app.route('/pos')
@login_required
def pos():
    categories = Category.query.filter_by(is_active=True).all()
    products = Product.query.filter_by(is_active=True).all()
    customers = Customer.query.filter_by(is_active=True).all()

    return render_template('pos/index.html',
                         categories=categories,
                         products=products,
                         customers=customers)

@app.route('/api/search_product')
@login_required
def search_product():
    query = request.args.get('q', '')
    barcode = request.args.get('barcode', '')

    if barcode:
        product = Product.query.filter_by(barcode=barcode, is_active=True).first()
        if product:
            return jsonify({
                'success': True,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'price': product.selling_price,
                    'stock': product.stock_quantity,
                    'barcode': product.barcode
                }
            })

    if query:
        products = Product.query.filter(
            Product.name.contains(query),
            Product.is_active == True
        ).limit(10).all()

        return jsonify({
            'success': True,
            'products': [{
                'id': p.id,
                'name': p.name,
                'price': p.selling_price,
                'stock': p.stock_quantity,
                'barcode': p.barcode
            } for p in products]
        })

    return jsonify({'success': False, 'message': 'لم يتم العثور على منتجات'})

@app.route('/api/create_invoice', methods=['POST'])
@login_required
def create_invoice():
    try:
        data = request.get_json()
        language = get_user_language()

        # توليد رقم فاتورة قطري
        invoice_number = generate_invoice_number()

        # حساب الضريبة (إذا كانت مفعلة)
        subtotal = data['subtotal']
        tax_amount = 0.0
        if QATAR_CONFIG['VAT_ENABLED']:
            tax_amount = subtotal * QATAR_CONFIG['VAT_RATE']

        total_amount = subtotal + tax_amount - data.get('discount_amount', 0)

        invoice = Invoice(
            invoice_number=invoice_number,
            customer_id=data.get('customer_id'),
            user_id=current_user.id,
            subtotal=subtotal,
            tax_amount=tax_amount,
            discount_amount=data.get('discount_amount', 0),
            total_amount=total_amount,
            payment_method=data['payment_method'],
            notes=data.get('notes', '')
        )

        db.session.add(invoice)
        db.session.flush()

        for item in data['items']:
            invoice_item = InvoiceItem(
                invoice_id=invoice.id,
                product_id=item['product_id'],
                quantity=item['quantity'],
                unit_price=item['unit_price'],
                total_price=item['total_price']
            )
            db.session.add(invoice_item)

            product = Product.query.get(item['product_id'])
            if product:
                product.stock_quantity -= item['quantity']

                stock_movement = StockMovement(
                    product_id=product.id,
                    movement_type='out',
                    quantity=item['quantity'],
                    reference_type='invoice',
                    reference_id=invoice.id,
                    user_id=current_user.id,
                    notes=f'بيع - فاتورة رقم {invoice_number}' if language == 'ar' else f'Sale - Invoice {invoice_number}'
                )
                db.session.add(stock_movement)

        db.session.commit()

        success_msg = 'تم إنشاء الفاتورة بنجاح' if language == 'ar' else 'Invoice created successfully'
        return jsonify({
            'success': True,
            'invoice_id': invoice.id,
            'invoice_number': invoice_number,
            'total_amount': format_currency(total_amount),
            'message': success_msg
        })

    except Exception as e:
        db.session.rollback()
        error_msg = f'حدث خطأ: {str(e)}' if get_user_language() == 'ar' else f'Error occurred: {str(e)}'
        return jsonify({
            'success': False,
            'message': error_msg
        })

@app.route('/products')
@login_required
def products():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)

    query = Product.query

    if search:
        query = query.filter(Product.name.contains(search))

    if category_id:
        query = query.filter_by(category_id=category_id)

    products = query.paginate(
        page=page, per_page=20, error_out=False
    )

    categories = Category.query.filter_by(is_active=True).all()

    return render_template('products/index.html',
                         products=products,
                         categories=categories,
                         search=search,
                         selected_category=category_id)

@app.route('/invoices')
@login_required
def invoices():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Invoice.query

    if search:
        query = query.join(Customer).filter(
            db.or_(
                Invoice.invoice_number.contains(search),
                Customer.name.contains(search)
            )
        )

    invoices = query.order_by(desc(Invoice.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('invoices/index.html', invoices=invoices, search=search)

@app.route('/invoice/<int:invoice_id>')
@login_required
def view_invoice(invoice_id):
    invoice = Invoice.query.get_or_404(invoice_id)
    language = get_user_language()

    return render_template('invoices/view.html',
                         invoice=invoice,
                         qatar_config=QATAR_CONFIG,
                         language=language,
                         format_currency=format_currency)

@app.route('/invoice/<int:invoice_id>/print')
@login_required
def print_invoice(invoice_id):
    invoice = Invoice.query.get_or_404(invoice_id)
    language = get_user_language()

    return render_template('invoices/print.html',
                         invoice=invoice,
                         qatar_config=QATAR_CONFIG,
                         language=language,
                         format_currency=format_currency)

@app.route('/invoice/<int:invoice_id>/thermal')
@login_required
def thermal_invoice(invoice_id):
    """طباعة فاتورة حرارية قطرية"""
    invoice = Invoice.query.get_or_404(invoice_id)
    language = get_user_language()

    return render_template('invoices/thermal.html',
                         invoice=invoice,
                         qatar_config=QATAR_CONFIG,
                         language=language,
                         format_currency=format_currency)

@app.route('/customers')
@login_required
def customers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Customer.query

    if search:
        query = query.filter(
            db.or_(
                Customer.name.contains(search),
                Customer.phone.contains(search)
            )
        )

    customers = query.paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('customers/index.html', customers=customers, search=search)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    language = get_user_language()

    if request.method == 'POST':
        try:
            # تنسيق رقم الهاتف القطري
            phone = request.form.get('phone', '')
            if phone:
                phone = format_phone_qatar(phone)
                if not validate_qatar_phone(phone):
                    error_msg = 'رقم الهاتف غير صحيح' if language == 'ar' else 'Invalid phone number'
                    flash(error_msg, 'error')
                    return render_template('customers/add.html', qatar_config=QATAR_CONFIG)

            # التحقق من رقم الهوية القطرية
            qid = request.form.get('qid', '')
            if qid and not validate_qatar_qid(qid):
                error_msg = 'رقم الهوية غير صحيح' if language == 'ar' else 'Invalid QID number'
                flash(error_msg, 'error')
                return render_template('customers/add.html', qatar_config=QATAR_CONFIG)

            customer = Customer(
                name=request.form['name'],
                name_en=request.form.get('name_en', ''),
                phone=phone,
                email=request.form.get('email'),
                address=request.form.get('address'),
                city=request.form.get('city', QATAR_CONFIG['DEFAULT_CITY']),
                qid=qid,
                nationality=request.form.get('nationality', 'قطري'),
                credit_balance=float(request.form.get('credit_balance', 0)),
                is_active=bool(request.form.get('is_active', True))
            )

            db.session.add(customer)
            db.session.commit()

            success_msg = 'تم إضافة العميل بنجاح' if language == 'ar' else 'Customer added successfully'
            flash(success_msg, 'success')
            return redirect(url_for('customers'))

        except Exception as e:
            db.session.rollback()
            error_msg = f'حدث خطأ: {str(e)}' if language == 'ar' else f'Error occurred: {str(e)}'
            flash(error_msg, 'error')

    return render_template('customers/add.html', qatar_config=QATAR_CONFIG)

@app.route('/customers/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        try:
            customer.name = request.form['name']
            customer.phone = request.form.get('phone')
            customer.email = request.form.get('email')
            customer.address = request.form.get('address')
            customer.credit_balance = float(request.form.get('credit_balance', 0))
            customer.is_active = bool(request.form.get('is_active'))

            db.session.commit()
            flash('تم تحديث بيانات العميل بنجاح', 'success')
            return redirect(url_for('customers'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('customers/edit.html', customer=customer, qatar_config=QATAR_CONFIG)

@app.route('/settings')
@login_required
def settings():
    """إعدادات النظام القطري"""
    language = get_user_language()

    # جلب الإعدادات الحالية من قاعدة البيانات
    settings = {}
    for setting in SystemSettings.query.all():
        settings[setting.key] = setting.value

    return render_template('settings/index.html',
                         settings=settings,
                         qatar_config=QATAR_CONFIG,
                         language=language)

@app.route('/settings/update', methods=['POST'])
@login_required
def update_settings():
    """تحديث إعدادات النظام"""
    language = get_user_language()

    try:
        # تحديث إعدادات الضريبة
        vat_enabled = bool(request.form.get('vat_enabled'))
        vat_rate = float(request.form.get('vat_rate', 0))

        # تحديث معلومات الشركة
        company_name = request.form.get('company_name', QATAR_CONFIG['COMPANY_NAME'])
        company_name_en = request.form.get('company_name_en', QATAR_CONFIG['COMPANY_NAME_EN'])
        cr_number = request.form.get('cr_number', QATAR_CONFIG['CR_NUMBER'])
        phone = request.form.get('phone', QATAR_CONFIG['PHONE'])
        whatsapp = request.form.get('whatsapp', QATAR_CONFIG['WHATSAPP'])
        email = request.form.get('email', QATAR_CONFIG['EMAIL'])

        # التحقق من أرقام الهاتف القطرية
        if phone and not validate_qatar_phone(phone):
            error_msg = 'رقم الهاتف غير صحيح' if language == 'ar' else 'Invalid phone number'
            flash(error_msg, 'error')
            return redirect(url_for('settings'))

        if whatsapp and not validate_qatar_phone(whatsapp):
            error_msg = 'رقم الواتساب غير صحيح' if language == 'ar' else 'Invalid WhatsApp number'
            flash(error_msg, 'error')
            return redirect(url_for('settings'))

        # حفظ الإعدادات في قاعدة البيانات
        settings_to_update = {
            'vat_enabled': str(vat_enabled),
            'vat_rate': str(vat_rate),
            'company_name': company_name,
            'company_name_en': company_name_en,
            'cr_number': cr_number,
            'phone': phone,
            'whatsapp': whatsapp,
            'email': email
        }

        for key, value in settings_to_update.items():
            setting = SystemSettings.query.filter_by(key=key).first()
            if setting:
                setting.value = value
            else:
                setting = SystemSettings(key=key, value=value)
                db.session.add(setting)

        db.session.commit()

        success_msg = 'تم تحديث الإعدادات بنجاح' if language == 'ar' else 'Settings updated successfully'
        flash(success_msg, 'success')

    except Exception as e:
        db.session.rollback()
        error_msg = f'حدث خطأ: {str(e)}' if language == 'ar' else f'Error occurred: {str(e)}'
        flash(error_msg, 'error')

    return redirect(url_for('settings'))

@app.route('/api/cities')
def get_qatar_cities():
    """API للحصول على المدن القطرية"""
    return jsonify({
        'cities': QATAR_CONFIG['CITIES'],
        'default_city': QATAR_CONFIG['DEFAULT_CITY']
    })

@app.route('/reports')
@login_required
def reports():
    return render_template('reports/index.html')

@app.route('/reports/sales')
@login_required
def sales_report():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    payment_method = request.args.get('payment_method')

    query = Invoice.query.filter_by(payment_status='paid')

    if start_date:
        query = query.filter(Invoice.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))

    if end_date:
        query = query.filter(Invoice.created_at <= datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S'))

    if payment_method:
        query = query.filter_by(payment_method=payment_method)

    invoices = query.order_by(desc(Invoice.created_at)).all()

    total_sales = sum(invoice.total_amount for invoice in invoices)
    total_invoices = len(invoices)

    return render_template('reports/sales.html',
                         invoices=invoices,
                         total_sales=total_sales,
                         total_invoices=total_invoices,
                         start_date=start_date,
                         end_date=end_date)

@app.route('/reports/products')
@login_required
def products_report():
    return render_template('reports/products.html')

@app.route('/reports/customers')
@login_required
def customers_report():
    return render_template('reports/customers.html')

@app.route('/reports/financial')
@login_required
def financial_report():
    return render_template('reports/financial.html')



def create_sample_data():
    """إنشاء بيانات تجريبية قطرية"""

    # إنشاء مستخدم افتراضي
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            role='admin'
        )
        admin.set_password('admin123')
        db.session.add(admin)
        print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")

    # إنشاء التصنيفات
    categories_data = [
        {'name': 'مشروبات', 'description': 'المشروبات الباردة والساخنة'},
        {'name': 'مأكولات', 'description': 'الوجبات والأطعمة'},
        {'name': 'حلويات', 'description': 'الحلويات والمعجنات'},
        {'name': 'مستلزمات', 'description': 'المستلزمات المتنوعة'}
    ]

    for cat_data in categories_data:
        if not Category.query.filter_by(name=cat_data['name']).first():
            category = Category(**cat_data)
            db.session.add(category)

    db.session.commit()

    # إنشاء المنتجات القطرية
    products_data = [
        {'name': 'كوكا كولا', 'category': 'مشروبات', 'price': 3.00, 'stock': 100, 'barcode': '1234567890001'},
        {'name': 'بيبسي', 'category': 'مشروبات', 'price': 3.00, 'stock': 80, 'barcode': '1234567890002'},
        {'name': 'ماء', 'category': 'مشروبات', 'price': 1.50, 'stock': 200, 'barcode': '1234567890003'},
        {'name': 'عصير برتقال', 'category': 'مشروبات', 'price': 8.00, 'stock': 50, 'barcode': '1234567890004'},
        {'name': 'قهوة عربية', 'category': 'مشروبات', 'price': 15.00, 'stock': 30, 'barcode': '1234567890005'},
        {'name': 'شاورما دجاج', 'category': 'مأكولات', 'price': 25.00, 'stock': 20, 'barcode': '1234567890006'},
        {'name': 'برجر لحم', 'category': 'مأكولات', 'price': 35.00, 'stock': 15, 'barcode': '1234567890007'},
        {'name': 'كنافة', 'category': 'حلويات', 'price': 20.00, 'stock': 10, 'barcode': '1234567890008'},
        {'name': 'بقلاوة', 'category': 'حلويات', 'price': 18.00, 'stock': 25, 'barcode': '1234567890009'}
    ]

    for prod_data in products_data:
        if not Product.query.filter_by(barcode=prod_data['barcode']).first():
            category = Category.query.filter_by(name=prod_data['category']).first()
            product = Product(
                name=prod_data['name'],
                barcode=prod_data['barcode'],
                category_id=category.id if category else None,
                purchase_price=prod_data['price'] * 0.7,
                selling_price=prod_data['price'],
                stock_quantity=prod_data['stock'],
                min_stock_level=5
            )
            db.session.add(product)

    # إنشاء عملاء تجريبيين قطريين
    customers_data = [
        {
            'name': 'أحمد محمد الكعبي',
            'name_en': 'Ahmed Mohammed Al-Kaabi',
            'phone': '+974 55123456',
            'email': '<EMAIL>',
            'city': 'الدوحة',
            'qid': '12345678901',
            'nationality': 'قطري'
        },
        {
            'name': 'فاطمة علي النعيمي',
            'name_en': 'Fatima Ali Al-Naimi',
            'phone': '+974 77654321',
            'email': '<EMAIL>',
            'city': 'الوكرة',
            'qid': '12345678902',
            'nationality': 'قطري'
        },
        {
            'name': 'محمد سعد الثاني',
            'name_en': 'Mohammed Saad Al-Thani',
            'phone': '+974 66789012',
            'email': '<EMAIL>',
            'city': 'الريان',
            'qid': '12345678903',
            'nationality': 'قطري'
        }
    ]

    for cust_data in customers_data:
        if not Customer.query.filter_by(qid=cust_data['qid']).first():
            customer = Customer(**cust_data)
            db.session.add(customer)

    # إنشاء إعدادات النظام القطري
    default_settings = {
        'country': 'Qatar',
        'currency': 'QAR',
        'language': 'Arabic',
        'vat_enabled': 'False',
        'vat_rate': '0.0',
        'company_name': QATAR_CONFIG['COMPANY_NAME'],
        'company_name_en': QATAR_CONFIG['COMPANY_NAME_EN'],
        'cr_number': QATAR_CONFIG['CR_NUMBER'],
        'phone': QATAR_CONFIG['PHONE'],
        'whatsapp': QATAR_CONFIG['WHATSAPP'],
        'email': QATAR_CONFIG['EMAIL']
    }

    for key, value in default_settings.items():
        if not SystemSettings.query.filter_by(key=key).first():
            setting = SystemSettings(key=key, value=value, description=f'إعداد {key}')
            db.session.add(setting)

    db.session.commit()
    print("✅ تم إنشاء البيانات التجريبية القطرية")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        create_sample_data()

        print("🇶🇦 نظام نقاط البيع القطري المحدث")
        print("=" * 60)
        print(f"🏢 الشركة: {QATAR_CONFIG['COMPANY_NAME']} - {QATAR_CONFIG['COMPANY_NAME_EN']}")
        print(f"🌐 العنوان: http://localhost:5000")
        print(f"👤 المستخدم: admin")
        print(f"🔑 كلمة المرور: admin123")
        print(f"💰 العملة: {QATAR_CONFIG['CURRENCY']} ({QATAR_CONFIG['CURRENCY_SYMBOL']})")
        print(f"📱 الهاتف: {QATAR_CONFIG['PHONE']}")
        print(f"💬 واتساب: {QATAR_CONFIG['WHATSAPP']}")
        print(f"🏛️ رقم السجل التجاري: {QATAR_CONFIG['CR_NUMBER']}")
        print(f"🏙️ المدن المدعومة: {', '.join(QATAR_CONFIG['CITIES'][:5])}...")
        print(f"🌐 اللغات المدعومة: {', '.join(QATAR_CONFIG['LANGUAGE_NAMES'].values())}")
        print(f"💸 الضريبة: {'مفعلة' if QATAR_CONFIG['VAT_ENABLED'] else 'غير مفعلة'}")
        print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
