from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء المتغيرات العامة
db = SQLAlchemy()
login_manager = LoginManager()

def create_app():
    """إنشاء وإعداد التطبيق"""
    app = Flask(__name__)
    
    # إعداد التطبيق
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///pos_system.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # تهيئة الإضافات
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    # استيراد النماذج والمسارات
    with app.app_context():
        from models import User
        from routes import *
        
        @login_manager.user_loader
        def load_user(user_id):
            return User.query.get(int(user_id))
        
        # إنشاء الجداول
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
