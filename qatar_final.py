#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقاط البيع النهائي - دولة قطر
Final Qatar POS System
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import re

app = Flask(__name__)
app.config['SECRET_KEY'] = 'qatar-pos-final-2025'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///qatar_final.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعدادات قطر
QATAR_CONFIG = {
    'COMPANY_NAME': 'لمسة الخليج',
    'COMPANY_NAME_EN': 'Gulf Touch',
    'CURRENCY_SYMBOL': 'ر.ق',
    'CR_NUMBER': '123456',
    'ADDRESS': 'الدوحة، دولة قطر',
    'PHONE': '+974 55123456',
    'WHATSAPP': '+974 77123456',
    'EMAIL': '<EMAIL>',
    'CITIES': ['الدوحة', 'الوكرة', 'الخور', 'الريان', 'أم صلال', 'الشمال', 'الضعاين'],
    'DATETIME_FORMAT': '%d/%m/%Y %H:%M'
}

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='cashier')
    city = db.Column(db.String(50), default='الدوحة')
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    price = db.Column(db.Float, nullable=False)
    stock = db.Column(db.Integer, nullable=False, default=0)
    category = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    city = db.Column(db.String(50), default='الدوحة')
    qid = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    total = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    customer = db.relationship('Customer', backref='sales')
    user = db.relationship('User', backref='sales')

class SaleItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sale.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    
    sale = db.relationship('Sale', backref='items')
    product = db.relationship('Product', backref='sale_items')

# دوال مساعدة
def format_currency(amount):
    return f"ر.ق {amount:.2f}"

def format_phone(phone):
    if phone and not phone.startswith('+974'):
        clean_phone = re.sub(r'[^\d]', '', phone)
        if len(clean_phone) == 8:
            return f"+974 {clean_phone}"
    return phone

def generate_invoice_number():
    last_sale = Sale.query.order_by(Sale.id.desc()).first()
    next_number = (last_sale.id + 1) if last_sale else 1
    return f"QA-{next_number:06d}"

# قوالب HTML
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - {{ config.COMPANY_NAME }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); 
            min-height: 100vh; 
            font-family: 'Arial', sans-serif;
        }
        .card { border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .company-logo { color: #8B0000; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container d-flex align-items-center justify-content-center min-vh-100">
        <div class="card" style="width: 100%; max-width: 450px;">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-cash-register fa-3x company-logo mb-3"></i>
                    <h3 class="company-logo">{{ config.COMPANY_NAME }}</h3>
                    <h4 class="text-muted">{{ config.COMPANY_NAME_EN }}</h4>
                    <p class="text-muted">نظام نقاط البيع - دولة قطر</p>
                </div>
                
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-danger">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-user me-2"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-lock me-2"></i>كلمة المرور
                        </label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-map-marker-alt me-2"></i>المدينة
                        </label>
                        <select class="form-select" name="city">
                            {% for city in config.CITIES %}
                            <option value="{{ city }}" {% if city == 'الدوحة' %}selected{% endif %}>{{ city }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-danger btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </button>
                    </div>
                </form>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        المستخدم: <strong>admin</strong><br>
                        كلمة المرور: <strong>admin123</strong>
                    </small>
                </div>
                
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="fas fa-phone me-1"></i>{{ config.PHONE }}<br>
                        <i class="fab fa-whatsapp me-1"></i>{{ config.WHATSAPP }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - {{ config.COMPANY_NAME }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; font-family: 'Arial', sans-serif; }
        .navbar { background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); }
        .card { border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }
        .stats-card { background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); color: white; }
        .btn-qatar { background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); border: none; color: white; }
        .btn-qatar:hover { background: linear-gradient(135deg, #DC143C 0%, #8B0000 100%); color: white; }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark">
        <div class="container">
            <span class="navbar-brand">
                <i class="fas fa-cash-register me-2"></i>
                {{ config.COMPANY_NAME }} - {{ config.COMPANY_NAME_EN }}
            </span>
            <div>
                <span class="text-white me-3">
                    <i class="fas fa-map-marker-alt me-1"></i>{{ session.user_city }}
                </span>
                <span class="text-white me-3">مرحباً، {{ session.user_name }}</span>
                <a href="/logout" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <!-- إحصائيات -->
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-box fa-2x mb-2"></i>
                        <h4>{{ products_count }}</h4>
                        <p>المنتجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h4>{{ sales_count }}</h4>
                        <p>المبيعات اليوم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-coins fa-2x mb-2"></i>
                        <h4>{{ total_sales }}</h4>
                        <p>إجمالي المبيعات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4>{{ customers_count }}</h4>
                        <p>العملاء</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- النماذج -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-plus me-2"></i>إضافة منتج</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/add_product">
                            <div class="mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">السعر ({{ config.CURRENCY_SYMBOL }})</label>
                                        <input type="number" class="form-control" name="price" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">الكمية</label>
                                        <input type="number" class="form-control" name="stock" required>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-qatar w-100">إضافة</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-shopping-cart me-2"></i>نقطة البيع</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/make_sale">
                            <div class="mb-3">
                                <label class="form-label">المنتج</label>
                                <select class="form-select" name="product_id" required>
                                    <option value="">اختر منتج</option>
                                    {% for product in products %}
                                    <option value="{{ product.id }}">{{ product.name }} - {{ format_currency(product.price) }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" min="1" required>
                            </div>
                            <button type="submit" class="btn btn-success w-100">بيع</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-user-plus me-2"></i>إضافة عميل</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/add_customer">
                            <div class="mb-3">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الجوال</label>
                                <div class="input-group">
                                    <span class="input-group-text">+974</span>
                                    <input type="tel" class="form-control" name="phone" placeholder="55123456">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-info w-100">إضافة</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قوائم البيانات -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header" style="color: #8B0000;">
                        <h5><i class="fas fa-list me-2"></i>المنتجات المتاحة</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% for product in products %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ product.name }}</strong>
                                    {% if product.name_en %}<br><small class="text-muted">{{ product.name_en }}</small>{% endif %}
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">{{ format_currency(product.price) }}</span>
                                </div>
                            </div>
                            <small class="text-muted">المخزون: {{ product.stock }}</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header" style="color: #8B0000;">
                        <h5><i class="fas fa-history me-2"></i>آخر المبيعات</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% for sale in recent_sales %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ sale.invoice_number }}</strong>
                                    <br><small class="text-muted">{{ sale.customer.name if sale.customer else 'عميل نقدي' }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">{{ format_currency(sale.total) }}</span>
                                    <br><small class="text-muted">{{ sale.created_at.strftime(config.DATETIME_FORMAT) }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

# المسارات
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect('/login')
    return redirect('/dashboard')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        city = request.form.get('city', 'الدوحة')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            session['user_id'] = user.id
            session['user_name'] = user.full_name
            session['user_city'] = city
            session['user_role'] = user.role

            user.city = city
            db.session.commit()

            return redirect('/dashboard')
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة')

    return render_template_string(LOGIN_TEMPLATE, config=QATAR_CONFIG)

@app.route('/logout')
def logout():
    session.clear()
    return redirect('/login')

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect('/login')

    products = Product.query.filter_by(is_active=True).all()
    customers = Customer.query.filter_by(is_active=True).all()

    today = datetime.now().date()
    today_sales = Sale.query.filter(
        db.func.date(Sale.created_at) == today
    ).all()

    products_count = len(products)
    sales_count = len(today_sales)
    total_sales = format_currency(sum(sale.total for sale in today_sales))
    customers_count = len(customers)

    recent_sales = Sale.query.order_by(Sale.created_at.desc()).limit(10).all()

    return render_template_string(DASHBOARD_TEMPLATE,
                                config=QATAR_CONFIG,
                                products=products,
                                customers=customers,
                                recent_sales=recent_sales,
                                products_count=products_count,
                                sales_count=sales_count,
                                total_sales=total_sales,
                                customers_count=customers_count,
                                format_currency=format_currency)

@app.route('/add_product', methods=['POST'])
def add_product():
    if 'user_id' not in session:
        return redirect('/login')

    name = request.form['name']
    name_en = request.form.get('name_en', '')
    price = float(request.form['price'])
    stock = int(request.form['stock'])
    category = request.form.get('category', '')

    product = Product(
        name=name,
        name_en=name_en,
        price=price,
        stock=stock,
        category=category
    )

    db.session.add(product)
    db.session.commit()

    return redirect('/dashboard')

@app.route('/add_customer', methods=['POST'])
def add_customer():
    if 'user_id' not in session:
        return redirect('/login')

    name = request.form['name']
    phone = request.form.get('phone', '')
    city = request.form.get('city', 'الدوحة')
    qid = request.form.get('qid', '')

    if phone:
        phone = format_phone(phone)

    customer = Customer(
        name=name,
        phone=phone,
        city=city,
        qid=qid
    )

    db.session.add(customer)
    db.session.commit()

    return redirect('/dashboard')

@app.route('/make_sale', methods=['POST'])
def make_sale():
    if 'user_id' not in session:
        return redirect('/login')

    product_id = int(request.form['product_id'])
    quantity = int(request.form['quantity'])
    payment_method = request.form.get('payment_method', 'cash')
    customer_id = request.form.get('customer_id')

    product = Product.query.get(product_id)
    if not product or product.stock < quantity:
        flash('المنتج غير متوفر أو الكمية غير كافية')
        return redirect('/dashboard')

    # حساب الإجمالي
    total = product.price * quantity

    # إنشاء الفاتورة
    invoice_number = generate_invoice_number()

    sale = Sale(
        invoice_number=invoice_number,
        customer_id=int(customer_id) if customer_id else None,
        user_id=session['user_id'],
        total=total,
        payment_method=payment_method
    )

    db.session.add(sale)
    db.session.flush()

    # إضافة عناصر الفاتورة
    sale_item = SaleItem(
        sale_id=sale.id,
        product_id=product.id,
        product_name=product.name,
        quantity=quantity,
        unit_price=product.price,
        total_price=total
    )

    db.session.add(sale_item)

    # تحديث المخزون
    product.stock -= quantity

    db.session.commit()

    return redirect(f'/invoice/{sale.id}')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                full_name='مدير النظام',
                role='admin',
                city='الدوحة'
            )
            admin.set_password('admin123')
            db.session.add(admin)

            # إضافة منتجات تجريبية قطرية
            products = [
                Product(name='كوكا كولا', name_en='Coca Cola', price=3.00, stock=100, category='مشروبات'),
                Product(name='بيبسي', name_en='Pepsi', price=3.00, stock=80, category='مشروبات'),
                Product(name='ماء', name_en='Water', price=1.50, stock=200, category='مشروبات'),
                Product(name='عصير برتقال', name_en='Orange Juice', price=8.00, stock=50, category='مشروبات'),
                Product(name='قهوة عربية', name_en='Arabic Coffee', price=15.00, stock=30, category='مشروبات'),
                Product(name='شاورما دجاج', name_en='Chicken Shawarma', price=25.00, stock=20, category='مأكولات'),
                Product(name='برجر لحم', name_en='Beef Burger', price=35.00, stock=15, category='مأكولات'),
                Product(name='كنافة', name_en='Kunafa', price=20.00, stock=10, category='حلويات'),
                Product(name='بقلاوة', name_en='Baklava', price=18.00, stock=25, category='حلويات')
            ]

            for product in products:
                db.session.add(product)

            # إضافة عملاء تجريبيين
            customers = [
                Customer(name='أحمد محمد الكعبي', phone='+974 55123456', city='الدوحة', qid='12345678901'),
                Customer(name='فاطمة علي النعيمي', phone='+974 77654321', city='الوكرة', qid='12*********'),
                Customer(name='محمد سعد الثاني', phone='+974 66789012', city='الريان', qid='12345678903')
            ]

            for customer in customers:
                db.session.add(customer)

            db.session.commit()
            print("✅ تم إنشاء البيانات التجريبية لنظام قطر")

    print("🇶🇦 نظام نقاط البيع النهائي - دولة قطر")
    print("=" * 60)
    print(f"🏢 الشركة: {QATAR_CONFIG['COMPANY_NAME']} - {QATAR_CONFIG['COMPANY_NAME_EN']}")
    print(f"🌐 العنوان: http://localhost:5000")
    print(f"👤 المستخدم: admin")
    print(f"🔑 كلمة المرور: admin123")
    print(f"💰 العملة: {QATAR_CONFIG['CURRENCY_SYMBOL']}")
    print(f"📱 الهاتف: {QATAR_CONFIG['PHONE']}")
    print(f"💬 واتساب: {QATAR_CONFIG['WHATSAPP']}")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
