#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقاط البيع القطري الشامل
Complete Qatar POS System
تحديد دولة قطر في نظام المبيعات POS وفقاً للمتطلبات المحددة
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import re
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'qatar-pos-complete-2025'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///qatar_pos_complete.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعدادات قطر الشاملة وفقاً للمتطلبات
QATAR_CONFIG = {
    # 1. العملة
    'COUNTRY': 'Qatar',
    'COUNTRY_AR': 'قطر',
    'CURRENCY': 'QAR',
    'CURRENCY_SYMBOL': 'ر.ق',
    'CURRENCY_FORMAT': 'ر.ق {:.2f}',
    
    # 2. اللغة (ثنائي اللغة)
    'DEFAULT_LANGUAGE': 'ar',
    'SUPPORTED_LANGUAGES': ['ar', 'en'],
    'LANGUAGE_NAMES': {'ar': 'العربية', 'en': 'English'},
    
    # 3. الضريبة (VAT)
    'VAT_ENABLED': False,  # لا توجد ضريبة حالياً في قطر
    'VAT_RATE': 0.0,
    'VAT_CONFIGURABLE': True,  # يمكن تفعيلها لاحقاً
    
    # 4. التاريخ والتقويم
    'DATE_FORMAT': '%d/%m/%Y',
    'DATETIME_FORMAT': '%d/%m/%Y %H:%M',
    'ALTERNATIVE_DATE_FORMAT': '%Y-%m-%d',
    'CALENDAR_TYPE': 'gregorian',
    
    # 5. معلومات المتجر والفواتير
    'COMPANY_NAME': 'لمسة الخليج',
    'COMPANY_NAME_EN': 'Gulf Touch',
    'CR_NUMBER': '123456',  # رقم السجل التجاري القطري
    'ADDRESS_AR': 'الدوحة، دولة قطر',
    'ADDRESS_EN': 'Doha, State of Qatar',
    'NATIONAL_ADDRESS': 'المنطقة 1، الشارع 123، المبنى 456، الدوحة، قطر',
    
    # 6. المواقع (المدن القطرية)
    'CITIES': [
        'الدوحة', 'الوكرة', 'الخور', 'الريان', 'أم صلال', 
        'الشمال', 'الضعاين', 'الشحانية', 'دخان', 'مسيعيد'
    ],
    'DEFAULT_CITY': 'الدوحة',
    
    # 7. أرقام الهاتف القطرية
    'PHONE_PREFIX': '+974',
    'PHONE_PATTERNS': [
        r'^\+974\s?[3-7]\d{7}$',  # +974 55123456
        r'^[3-7]\d{7}$'           # 55123456
    ],
    'PHONE_EXAMPLE': '+974 55123456',
    
    # معلومات التواصل
    'PHONE': '+974 55123456',
    'WHATSAPP': '+974 77123456',
    'EMAIL': '<EMAIL>',
    'WEBSITE': 'www.gulftouch.qa',
    
    # إعدادات الفاتورة القطرية
    'INVOICE_PREFIX': 'QA-INV-',
    'RECEIPT_PREFIX': 'QA-REC-',
    'INVOICE_FOOTER_AR': 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
    'INVOICE_FOOTER_EN': 'Thank you for your visit - We look forward to serving you again'
}

db = SQLAlchemy(app)

# نماذج قاعدة البيانات المحدثة للمتطلبات القطرية
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='cashier')
    city = db.Column(db.String(50), default='الدوحة')  # المدينة القطرية
    language = db.Column(db.String(5), default='ar')  # اللغة المفضلة
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))  # الاسم بالإنجليزية
    barcode = db.Column(db.String(50), unique=True)
    price = db.Column(db.Float, nullable=False)
    cost = db.Column(db.Float, default=0.0)
    stock = db.Column(db.Integer, nullable=False, default=0)
    min_stock = db.Column(db.Integer, default=5)
    category = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))  # الاسم بالإنجليزية
    phone = db.Column(db.String(20))  # رقم الهاتف القطري
    email = db.Column(db.String(120))
    city = db.Column(db.String(50), default='الدوحة')  # المدينة القطرية
    address = db.Column(db.Text)
    qid = db.Column(db.String(20))  # رقم الهوية القطرية
    nationality = db.Column(db.String(50), default='قطري')  # الجنسية
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subtotal = db.Column(db.Float, nullable=False)
    vat_amount = db.Column(db.Float, default=0.0)  # ضريبة القيمة المضافة
    discount_amount = db.Column(db.Float, default=0.0)
    total = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    customer = db.relationship('Customer', backref='sales')
    user = db.relationship('User', backref='sales')

class SaleItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sale.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    
    sale = db.relationship('Sale', backref='items')
    product = db.relationship('Product', backref='sale_items')

class SystemSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    updated_at = db.Column(db.DateTime, default=datetime.now)

# دوال مساعدة للمتطلبات القطرية
def format_currency(amount):
    """تنسيق العملة القطرية: ر.ق 100.00"""
    return QATAR_CONFIG['CURRENCY_FORMAT'].format(amount)

def format_phone_qatar(phone):
    """تنسيق رقم الهاتف القطري: +974 55XXXXXX"""
    if not phone:
        return phone
    
    # إزالة جميع الرموز غير الرقمية
    clean_phone = re.sub(r'[^\d]', '', phone)
    
    # إذا كان الرقم يبدأ بـ 974، إزالته
    if clean_phone.startswith('974'):
        clean_phone = clean_phone[3:]
    
    # التحقق من صحة الرقم القطري (8 أرقام تبدأ بـ 3-7)
    if len(clean_phone) == 8 and clean_phone[0] in '34567':
        return f"+974 {clean_phone}"
    
    return phone

def validate_qatar_phone(phone):
    """التحقق من صحة رقم الهاتف القطري"""
    if not phone:
        return True
    
    for pattern in QATAR_CONFIG['PHONE_PATTERNS']:
        if re.match(pattern, phone):
            return True
    return False

def validate_qatar_qid(qid):
    """التحقق من صحة رقم الهوية القطرية (11 رقم)"""
    if not qid:
        return True
    return bool(re.match(r'^\d{11}$', qid))

def generate_invoice_number():
    """توليد رقم فاتورة قطري"""
    last_sale = Sale.query.order_by(Sale.id.desc()).first()
    next_number = (last_sale.id + 1) if last_sale else 1
    return f"{QATAR_CONFIG['INVOICE_PREFIX']}{next_number:06d}"

def get_user_language():
    """الحصول على لغة المستخدم"""
    return session.get('language', QATAR_CONFIG['DEFAULT_LANGUAGE'])

def calculate_vat(amount):
    """حساب ضريبة القيمة المضافة (إذا كانت مفعلة)"""
    if QATAR_CONFIG['VAT_ENABLED']:
        return amount * QATAR_CONFIG['VAT_RATE']
    return 0.0

# قوالب HTML للواجهات
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="{{ 'ar' if language == 'ar' else 'en' }}" dir="{{ 'rtl' if language == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ 'تسجيل الدخول' if language == 'ar' else 'Login' }} - {{ config.COMPANY_NAME }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap{{ '.rtl' if language == 'ar' else '' }}.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); 
            min-height: 100vh; 
            font-family: 'Arial', sans-serif;
        }
        .card { border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .company-logo { color: #8B0000; font-weight: bold; }
        .qatar-flag { background: linear-gradient(90deg, #8B0000 0%, #8B0000 100%); height: 5px; }
        .language-switcher { position: absolute; top: 20px; right: 20px; }
    </style>
</head>
<body>
    <div class="language-switcher">
        <a href="/set_language/ar" class="btn btn-outline-light btn-sm {{ 'active' if language == 'ar' else '' }}">العربية</a>
        <a href="/set_language/en" class="btn btn-outline-light btn-sm {{ 'active' if language == 'en' else '' }}">English</a>
    </div>
    
    <div class="container d-flex align-items-center justify-content-center min-vh-100">
        <div class="card" style="width: 100%; max-width: 450px;">
            <div class="qatar-flag"></div>
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-cash-register fa-3x company-logo mb-3"></i>
                    <h3 class="company-logo">{{ config.COMPANY_NAME }}</h3>
                    <h4 class="text-muted">{{ config.COMPANY_NAME_EN }}</h4>
                    <p class="text-muted">
                        {{ 'نظام نقاط البيع - دولة قطر' if language == 'ar' else 'POS System - State of Qatar' }}
                    </p>
                    <small class="text-muted">
                        {{ 'رقم السجل التجاري:' if language == 'ar' else 'CR Number:' }} {{ config.CR_NUMBER }}
                    </small>
                </div>
                
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-danger">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-user me-2"></i>
                            {{ 'اسم المستخدم' if language == 'ar' else 'Username' }}
                        </label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-lock me-2"></i>
                            {{ 'كلمة المرور' if language == 'ar' else 'Password' }}
                        </label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            {{ 'المدينة' if language == 'ar' else 'City' }}
                        </label>
                        <select class="form-select" name="city">
                            {% for city in config.CITIES %}
                            <option value="{{ city }}" {% if city == config.DEFAULT_CITY %}selected{% endif %}>{{ city }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-danger btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            {{ 'تسجيل الدخول' if language == 'ar' else 'Login' }}
                        </button>
                    </div>
                </form>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        {{ 'المستخدم:' if language == 'ar' else 'Username:' }} <strong>admin</strong><br>
                        {{ 'كلمة المرور:' if language == 'ar' else 'Password:' }} <strong>admin123</strong>
                    </small>
                </div>
                
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="fas fa-phone me-1"></i>{{ config.PHONE }}<br>
                        <i class="fab fa-whatsapp me-1"></i>{{ config.WHATSAPP }}<br>
                        <i class="fas fa-envelope me-1"></i>{{ config.EMAIL }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

# المسارات الأساسية
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/set_language/<language>')
def set_language(language):
    """تبديل اللغة"""
    if language in QATAR_CONFIG['SUPPORTED_LANGUAGES']:
        session['language'] = language
        flash('تم تغيير اللغة بنجاح' if language == 'ar' else 'Language changed successfully', 'success')
    return redirect(request.referrer or url_for('index'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    language = session.get('language', QATAR_CONFIG['DEFAULT_LANGUAGE'])

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        city = request.form.get('city', QATAR_CONFIG['DEFAULT_CITY'])

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            session['user_id'] = user.id
            session['user_name'] = user.full_name
            session['user_city'] = city
            session['user_role'] = user.role
            session['language'] = user.language

            # تحديث مدينة المستخدم
            user.city = city
            db.session.commit()

            welcome_msg = f'مرحباً {user.full_name}!' if user.language == 'ar' else f'Welcome {user.full_name}!'
            flash(welcome_msg, 'success')
            return redirect(url_for('dashboard'))
        else:
            error_msg = 'اسم المستخدم أو كلمة المرور غير صحيحة' if language == 'ar' else 'Invalid username or password'
            flash(error_msg, 'error')

    return render_template_string(LOGIN_TEMPLATE, config=QATAR_CONFIG, language=language)

@app.route('/logout')
def logout():
    language = get_user_language()
    session.clear()
    msg = 'تم تسجيل الخروج بنجاح' if language == 'ar' else 'Logged out successfully'
    flash(msg, 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    language = get_user_language()

    # إحصائيات اليوم
    today = datetime.now().date()
    today_sales = Sale.query.filter(
        db.func.date(Sale.created_at) == today
    ).all()

    products = Product.query.filter_by(is_active=True).all()
    customers = Customer.query.filter_by(is_active=True).all()
    recent_sales = Sale.query.order_by(Sale.created_at.desc()).limit(10).all()

    stats = {
        'products_count': len(products),
        'sales_count': len(today_sales),
        'total_sales': format_currency(sum(sale.total for sale in today_sales)),
        'customers_count': len(customers)
    }

    # قالب مبسط للوحة التحكم
    simple_dashboard = '''
    <!DOCTYPE html>
    <html lang="{{ 'ar' if language == 'ar' else 'en' }}" dir="{{ 'rtl' if language == 'ar' else 'ltr' }}">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{{ 'لوحة التحكم' if language == 'ar' else 'Dashboard' }} - {{ config.COMPANY_NAME }}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap{{ '.rtl' if language == 'ar' else '' }}.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; font-family: 'Arial', sans-serif; }
            .navbar { background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); }
            .card { border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }
            .stats-card { background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); color: white; }
            .btn-qatar { background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); border: none; color: white; }
            .btn-qatar:hover { background: linear-gradient(135deg, #DC143C 0%, #8B0000 100%); color: white; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-dark">
            <div class="container">
                <span class="navbar-brand">
                    <i class="fas fa-cash-register me-2"></i>
                    {{ config.COMPANY_NAME }} - {{ config.COMPANY_NAME_EN }}
                </span>
                <div>
                    <span class="text-white me-3">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ session.user_city }}
                    </span>
                    <span class="text-white me-3">
                        {{ 'مرحباً،' if language == 'ar' else 'Hello,' }} {{ session.user_name }}
                    </span>
                    <a href="{{ url_for('set_language', language='en' if language == 'ar' else 'ar') }}" class="btn btn-outline-light btn-sm me-2">
                        {{ 'EN' if language == 'ar' else 'عر' }}
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        {{ 'خروج' if language == 'ar' else 'Logout' }}
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- إحصائيات -->
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-box fa-2x mb-2"></i>
                            <h4>{{ stats.products_count }}</h4>
                            <p>{{ 'المنتجات' if language == 'ar' else 'Products' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <h4>{{ stats.sales_count }}</h4>
                            <p>{{ 'المبيعات اليوم' if language == 'ar' else 'Today Sales' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-coins fa-2x mb-2"></i>
                            <h4>{{ stats.total_sales }}</h4>
                            <p>{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h4>{{ stats.customers_count }}</h4>
                            <p>{{ 'العملاء' if language == 'ar' else 'Customers' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- النماذج -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5>
                                <i class="fas fa-plus me-2"></i>
                                {{ 'إضافة منتج' if language == 'ar' else 'Add Product' }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('add_product') }}">
                                <div class="mb-3">
                                    <label class="form-label">
                                        {{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}
                                    </label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">
                                                {{ 'السعر' if language == 'ar' else 'Price' }} ({{ config.CURRENCY_SYMBOL }})
                                            </label>
                                            <input type="number" class="form-control" name="price" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">
                                                {{ 'الكمية' if language == 'ar' else 'Quantity' }}
                                            </label>
                                            <input type="number" class="form-control" name="stock" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-qatar w-100">
                                    {{ 'إضافة' if language == 'ar' else 'Add' }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5>
                                <i class="fas fa-shopping-cart me-2"></i>
                                {{ 'نقطة البيع' if language == 'ar' else 'Point of Sale' }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('make_sale') }}">
                                <div class="mb-3">
                                    <label class="form-label">
                                        {{ 'المنتج' if language == 'ar' else 'Product' }}
                                    </label>
                                    <select class="form-select" name="product_id" required>
                                        <option value="">
                                            {{ 'اختر منتج' if language == 'ar' else 'Select Product' }}
                                        </option>
                                        {% for product in products %}
                                        <option value="{{ product.id }}">
                                            {{ product.name }} - {{ format_currency(product.price) }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">
                                        {{ 'الكمية' if language == 'ar' else 'Quantity' }}
                                    </label>
                                    <input type="number" class="form-control" name="quantity" min="1" required>
                                </div>
                                <button type="submit" class="btn btn-success w-100">
                                    {{ 'بيع' if language == 'ar' else 'Sell' }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5>
                                <i class="fas fa-user-plus me-2"></i>
                                {{ 'إضافة عميل' if language == 'ar' else 'Add Customer' }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('add_customer') }}">
                                <div class="mb-3">
                                    <label class="form-label">
                                        {{ 'اسم العميل' if language == 'ar' else 'Customer Name' }}
                                    </label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">
                                        {{ 'رقم الجوال' if language == 'ar' else 'Mobile Number' }}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">+974</span>
                                        <input type="tel" class="form-control" name="phone" placeholder="55123456">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-info w-100">
                                    {{ 'إضافة' if language == 'ar' else 'Add' }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

    return render_template_string(simple_dashboard,
                                config=QATAR_CONFIG,
                                language=language,
                                products=products,
                                customers=customers,
                                recent_sales=recent_sales,
                                stats=stats,
                                format_currency=format_currency)

@app.route('/add_product', methods=['POST'])
def add_product():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    language = get_user_language()

    try:
        name = request.form['name']
        name_en = request.form.get('name_en', '')
        price = float(request.form['price'])
        stock = int(request.form['stock'])
        category = request.form.get('category', '')

        product = Product(
            name=name,
            name_en=name_en,
            price=price,
            stock=stock,
            category=category
        )

        db.session.add(product)
        db.session.commit()

        success_msg = 'تم إضافة المنتج بنجاح' if language == 'ar' else 'Product added successfully'
        flash(success_msg, 'success')

    except Exception as e:
        db.session.rollback()
        error_msg = f'حدث خطأ: {str(e)}' if language == 'ar' else f'Error occurred: {str(e)}'
        flash(error_msg, 'error')

    return redirect(url_for('dashboard'))

@app.route('/add_customer', methods=['POST'])
def add_customer():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    language = get_user_language()

    try:
        name = request.form['name']
        phone = request.form.get('phone', '')
        city = request.form.get('city', QATAR_CONFIG['DEFAULT_CITY'])
        qid = request.form.get('qid', '')

        # تنسيق رقم الهاتف القطري
        if phone:
            phone = format_phone_qatar(phone)
            if not validate_qatar_phone(phone):
                error_msg = 'رقم الهاتف غير صحيح' if language == 'ar' else 'Invalid phone number'
                flash(error_msg, 'error')
                return redirect(url_for('dashboard'))

        # التحقق من رقم الهوية القطرية
        if qid and not validate_qatar_qid(qid):
            error_msg = 'رقم الهوية غير صحيح' if language == 'ar' else 'Invalid QID number'
            flash(error_msg, 'error')
            return redirect(url_for('dashboard'))

        customer = Customer(
            name=name,
            phone=phone,
            city=city,
            qid=qid
        )

        db.session.add(customer)
        db.session.commit()

        success_msg = 'تم إضافة العميل بنجاح' if language == 'ar' else 'Customer added successfully'
        flash(success_msg, 'success')

    except Exception as e:
        db.session.rollback()
        error_msg = f'حدث خطأ: {str(e)}' if language == 'ar' else f'Error occurred: {str(e)}'
        flash(error_msg, 'error')

    return redirect(url_for('dashboard'))

@app.route('/make_sale', methods=['POST'])
def make_sale():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    language = get_user_language()

    try:
        product_id = int(request.form['product_id'])
        quantity = int(request.form['quantity'])
        payment_method = request.form.get('payment_method', 'cash')
        customer_id = request.form.get('customer_id')

        product = Product.query.get(product_id)
        if not product or product.stock < quantity:
            error_msg = 'المنتج غير متوفر أو الكمية غير كافية' if language == 'ar' else 'Product unavailable or insufficient quantity'
            flash(error_msg, 'error')
            return redirect(url_for('dashboard'))

        # حساب الإجمالي
        subtotal = product.price * quantity
        vat_amount = calculate_vat(subtotal)
        total = subtotal + vat_amount

        # إنشاء الفاتورة القطرية
        invoice_number = generate_invoice_number()

        sale = Sale(
            invoice_number=invoice_number,
            customer_id=int(customer_id) if customer_id else None,
            user_id=session['user_id'],
            subtotal=subtotal,
            vat_amount=vat_amount,
            total=total,
            payment_method=payment_method
        )

        db.session.add(sale)
        db.session.flush()

        # إضافة عناصر الفاتورة
        sale_item = SaleItem(
            sale_id=sale.id,
            product_id=product.id,
            product_name=product.name,
            quantity=quantity,
            unit_price=product.price,
            total_price=subtotal
        )

        db.session.add(sale_item)

        # تحديث المخزون
        product.stock -= quantity

        db.session.commit()

        success_msg = 'تم إتمام البيع بنجاح' if language == 'ar' else 'Sale completed successfully'
        flash(success_msg, 'success')

        return redirect(url_for('view_invoice', sale_id=sale.id))

    except Exception as e:
        db.session.rollback()
        error_msg = f'حدث خطأ: {str(e)}' if language == 'ar' else f'Error occurred: {str(e)}'
        flash(error_msg, 'error')

    return redirect(url_for('dashboard'))

@app.route('/invoice/<int:sale_id>')
def view_invoice(sale_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    language = get_user_language()
    sale = Sale.query.get_or_404(sale_id)

    # نموذج الفاتورة القطرية
    invoice_html = '''
    <!DOCTYPE html>
    <html lang="{{ 'ar' if language == 'ar' else 'en' }}" dir="{{ 'rtl' if language == 'ar' else 'ltr' }}">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{{ 'فاتورة' if language == 'ar' else 'Invoice' }} {{ sale.invoice_number }}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap{{ '.rtl' if language == 'ar' else '' }}.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            @media print { .no-print { display: none; } }
            .invoice-header { background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); color: white; }
            .qatar-border { border: 3px solid #8B0000; }
            .company-info { background-color: #f8f9fa; }
        </style>
    </head>
    <body>
        <div class="container mt-4">
            <div class="card qatar-border">
                <div class="card-header invoice-header text-center">
                    <div class="row">
                        <div class="col-md-8">
                            <h2>{{ config.COMPANY_NAME }}</h2>
                            <h3>{{ config.COMPANY_NAME_EN }}</h3>
                            <p class="mb-1">{{ config.ADDRESS_AR }}</p>
                            <p class="mb-1">{{ config.ADDRESS_EN }}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h4>{{ 'فاتورة' if language == 'ar' else 'INVOICE' }}</h4>
                            <p class="mb-1">{{ 'رقم السجل التجاري:' if language == 'ar' else 'CR Number:' }} {{ config.CR_NUMBER }}</p>
                            <p class="mb-0">{{ 'دولة قطر' if language == 'ar' else 'State of Qatar' }}</p>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="company-info p-3 rounded">
                                <h5>{{ 'معلومات الفاتورة:' if language == 'ar' else 'Invoice Information:' }}</h5>
                                <p><strong>{{ 'رقم الفاتورة:' if language == 'ar' else 'Invoice Number:' }}</strong> {{ sale.invoice_number }}</p>
                                <p><strong>{{ 'التاريخ:' if language == 'ar' else 'Date:' }}</strong> {{ sale.created_at.strftime(config.DATETIME_FORMAT) }}</p>
                                <p><strong>{{ 'الكاشير:' if language == 'ar' else 'Cashier:' }}</strong> {{ sale.user.full_name }}</p>
                                <p><strong>{{ 'المدينة:' if language == 'ar' else 'City:' }}</strong> {{ sale.user.city }}</p>
                                <p><strong>{{ 'طريقة الدفع:' if language == 'ar' else 'Payment Method:' }}</strong>
                                    {% if sale.payment_method == 'cash' %}{{ 'نقدي' if language == 'ar' else 'Cash' }}
                                    {% elif sale.payment_method == 'card' %}{{ 'بطاقة' if language == 'ar' else 'Card' }}
                                    {% else %}{{ 'تحويل' if language == 'ar' else 'Transfer' }}{% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="company-info p-3 rounded">
                                <h5>{{ 'معلومات التواصل:' if language == 'ar' else 'Contact Information:' }}</h5>
                                <p><strong>{{ 'الهاتف:' if language == 'ar' else 'Phone:' }}</strong> {{ config.PHONE }}</p>
                                <p><strong>{{ 'واتساب:' if language == 'ar' else 'WhatsApp:' }}</strong> {{ config.WHATSAPP }}</p>
                                <p><strong>{{ 'البريد الإلكتروني:' if language == 'ar' else 'Email:' }}</strong> {{ config.EMAIL }}</p>
                                <p><strong>{{ 'الموقع الإلكتروني:' if language == 'ar' else 'Website:' }}</strong> {{ config.WEBSITE }}</p>
                                {% if sale.customer %}
                                <hr>
                                <h6>{{ 'معلومات العميل:' if language == 'ar' else 'Customer Information:' }}</h6>
                                <p><strong>{{ 'الاسم:' if language == 'ar' else 'Name:' }}</strong> {{ sale.customer.name }}</p>
                                {% if sale.customer.phone %}<p><strong>{{ 'الهاتف:' if language == 'ar' else 'Phone:' }}</strong> {{ sale.customer.phone }}</p>{% endif %}
                                {% if sale.customer.city %}<p><strong>{{ 'المدينة:' if language == 'ar' else 'City:' }}</strong> {{ sale.customer.city }}</p>{% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                                    <th>{{ 'السعر' if language == 'ar' else 'Price' }}</th>
                                    <th>{{ 'الكمية' if language == 'ar' else 'Quantity' }}</th>
                                    <th>{{ 'الإجمالي' if language == 'ar' else 'Total' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sale.items %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.product_name }}</td>
                                    <td>{{ format_currency(item.unit_price) }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ format_currency(item.total_price) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <table class="table">
                                <tr>
                                    <td><strong>{{ 'المجموع الفرعي:' if language == 'ar' else 'Subtotal:' }}</strong></td>
                                    <td class="text-end"><strong>{{ format_currency(sale.subtotal) }}</strong></td>
                                </tr>
                                {% if sale.vat_amount > 0 %}
                                <tr>
                                    <td><strong>{{ 'ضريبة القيمة المضافة:' if language == 'ar' else 'VAT:' }}</strong></td>
                                    <td class="text-end"><strong>{{ format_currency(sale.vat_amount) }}</strong></td>
                                </tr>
                                {% endif %}
                                {% if sale.discount_amount > 0 %}
                                <tr>
                                    <td><strong>{{ 'الخصم:' if language == 'ar' else 'Discount:' }}</strong></td>
                                    <td class="text-end"><strong>-{{ format_currency(sale.discount_amount) }}</strong></td>
                                </tr>
                                {% endif %}
                                <tr class="table-dark">
                                    <td><strong>{{ 'الإجمالي النهائي:' if language == 'ar' else 'Grand Total:' }}</strong></td>
                                    <td class="text-end"><strong>{{ format_currency(sale.total) }}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <p><strong>{{ config.INVOICE_FOOTER_AR if language == 'ar' else config.INVOICE_FOOTER_EN }}</strong></p>
                        <p class="text-muted">{{ 'تم إنشاء هذه الفاتورة إلكترونياً' if language == 'ar' else 'This invoice was generated electronically' }}</p>
                        <small class="text-muted">{{ 'نظام نقاط البيع القطري' if language == 'ar' else 'Qatar POS System' }} - {{ config.COMPANY_NAME }}</small>
                    </div>
                </div>
            </div>

            <div class="text-center mt-3 no-print">
                <button onclick="window.print()" class="btn btn-primary me-2">
                    <i class="fas fa-print me-2"></i>{{ 'طباعة' if language == 'ar' else 'Print' }}
                </button>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-{{ 'right' if language == 'ar' else 'left' }} me-2"></i>{{ 'العودة' if language == 'ar' else 'Back' }}
                </a>
            </div>
        </div>
    </body>
    </html>
    '''

    return render_template_string(invoice_html,
                                sale=sale,
                                config=QATAR_CONFIG,
                                language=language,
                                format_currency=format_currency)

def create_sample_data():
    """إنشاء بيانات تجريبية قطرية"""

    # إنشاء مستخدم افتراضي
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            full_name='مدير النظام',
            role='admin',
            city='الدوحة',
            language='ar'
        )
        admin.set_password('admin123')
        db.session.add(admin)
        print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")

    # إنشاء المنتجات القطرية
    products_data = [
        {'name': 'كوكا كولا', 'name_en': 'Coca Cola', 'price': 3.00, 'stock': 100, 'category': 'مشروبات'},
        {'name': 'بيبسي', 'name_en': 'Pepsi', 'price': 3.00, 'stock': 80, 'category': 'مشروبات'},
        {'name': 'ماء', 'name_en': 'Water', 'price': 1.50, 'stock': 200, 'category': 'مشروبات'},
        {'name': 'عصير برتقال', 'name_en': 'Orange Juice', 'price': 8.00, 'stock': 50, 'category': 'مشروبات'},
        {'name': 'قهوة عربية', 'name_en': 'Arabic Coffee', 'price': 15.00, 'stock': 30, 'category': 'مشروبات'},
        {'name': 'شاورما دجاج', 'name_en': 'Chicken Shawarma', 'price': 25.00, 'stock': 20, 'category': 'مأكولات'},
        {'name': 'برجر لحم', 'name_en': 'Beef Burger', 'price': 35.00, 'stock': 15, 'category': 'مأكولات'},
        {'name': 'كنافة', 'name_en': 'Kunafa', 'price': 20.00, 'stock': 10, 'category': 'حلويات'},
        {'name': 'بقلاوة', 'name_en': 'Baklava', 'price': 18.00, 'stock': 25, 'category': 'حلويات'}
    ]

    for prod_data in products_data:
        if not Product.query.filter_by(name=prod_data['name']).first():
            product = Product(**prod_data)
            db.session.add(product)

    # إنشاء عملاء تجريبيين قطريين
    customers_data = [
        {
            'name': 'أحمد محمد الكعبي',
            'name_en': 'Ahmed Mohammed Al-Kaabi',
            'phone': '+974 55123456',
            'city': 'الدوحة',
            'qid': '12345678901',
            'nationality': 'قطري'
        },
        {
            'name': 'فاطمة علي النعيمي',
            'name_en': 'Fatima Ali Al-Naimi',
            'phone': '+974 77654321',
            'city': 'الوكرة',
            'qid': '12345678902',
            'nationality': 'قطري'
        },
        {
            'name': 'محمد سعد الثاني',
            'name_en': 'Mohammed Saad Al-Thani',
            'phone': '+974 66789012',
            'city': 'الريان',
            'qid': '12345678903',
            'nationality': 'قطري'
        }
    ]

    for cust_data in customers_data:
        if not Customer.query.filter_by(qid=cust_data['qid']).first():
            customer = Customer(**cust_data)
            db.session.add(customer)

    # إنشاء إعدادات النظام
    settings_data = {
        'vat_enabled': 'False',
        'vat_rate': '0.0',
        'company_name': QATAR_CONFIG['COMPANY_NAME'],
        'company_name_en': QATAR_CONFIG['COMPANY_NAME_EN'],
        'cr_number': QATAR_CONFIG['CR_NUMBER'],
        'phone': QATAR_CONFIG['PHONE'],
        'whatsapp': QATAR_CONFIG['WHATSAPP'],
        'email': QATAR_CONFIG['EMAIL']
    }

    for key, value in settings_data.items():
        if not SystemSettings.query.filter_by(key=key).first():
            setting = SystemSettings(key=key, value=value, description=f'إعداد {key}')
            db.session.add(setting)

    db.session.commit()
    print("✅ تم إنشاء البيانات التجريبية القطرية")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        create_sample_data()

        print("🇶🇦 نظام نقاط البيع القطري الشامل")
        print("=" * 70)
        print(f"🏢 الشركة: {QATAR_CONFIG['COMPANY_NAME']} - {QATAR_CONFIG['COMPANY_NAME_EN']}")
        print(f"🌐 العنوان: http://localhost:5000")
        print(f"👤 المستخدم: admin")
        print(f"🔑 كلمة المرور: admin123")
        print(f"💰 العملة: {QATAR_CONFIG['CURRENCY']} ({QATAR_CONFIG['CURRENCY_SYMBOL']})")
        print(f"📱 الهاتف: {QATAR_CONFIG['PHONE']}")
        print(f"💬 واتساب: {QATAR_CONFIG['WHATSAPP']}")
        print(f"📧 البريد الإلكتروني: {QATAR_CONFIG['EMAIL']}")
        print(f"🏛️ رقم السجل التجاري: {QATAR_CONFIG['CR_NUMBER']}")
        print(f"🏙️ المدن المدعومة: {', '.join(QATAR_CONFIG['CITIES'][:5])}...")
        print(f"🌐 اللغات المدعومة: {', '.join(QATAR_CONFIG['LANGUAGE_NAMES'].values())}")
        print(f"💸 الضريبة: {'مفعلة' if QATAR_CONFIG['VAT_ENABLED'] else 'غير مفعلة (يمكن تفعيلها لاحقاً)'}")
        print(f"📅 تنسيق التاريخ: {QATAR_CONFIG['DATE_FORMAT']}")
        print(f"📋 بادئة الفاتورة: {QATAR_CONFIG['INVOICE_PREFIX']}")
        print("=" * 70)
        print("✨ المميزات القطرية:")
        print("   • تنسيق العملة القطرية: ر.ق 100.00")
        print("   • دعم ثنائي اللغة (العربية/الإنجليزية)")
        print("   • التحقق من أرقام الهاتف القطرية")
        print("   • التحقق من أرقام الهوية القطرية")
        print("   • المدن القطرية المدعومة")
        print("   • فواتير مخصصة لدولة قطر")
        print("   • إعدادات ضريبة قابلة للتكوين")
        print("=" * 70)

    app.run(debug=True, host='0.0.0.0', port=5000)
