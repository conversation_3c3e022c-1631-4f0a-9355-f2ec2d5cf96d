from flask import render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_user, login_required, logout_user, current_user
from app import app, db
from models import User, Product, Category, Customer, Invoice, InvoiceItem, StockMovement, SystemSettings
from datetime import datetime, timedelta
from sqlalchemy import func, desc
import json

# الصفحة الرئيسية - إعادة توجيه إلى لوحة التحكم
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            next_page = request.args.get('next')
            flash(f'مرحباً {user.full_name}!', 'success')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

# لوحة التحكم الرئيسية
@app.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات اليوم
    today = datetime.now().date()
    today_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        func.date(Invoice.created_at) == today,
        Invoice.payment_status == 'paid'
    ).scalar() or 0
    
    today_invoices = Invoice.query.filter(
        func.date(Invoice.created_at) == today
    ).count()
    
    # إحصائيات الشهر
    month_start = today.replace(day=1)
    month_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.created_at >= month_start,
        Invoice.payment_status == 'paid'
    ).scalar() or 0
    
    # المنتجات منخفضة المخزون
    low_stock_products = Product.query.filter(
        Product.stock_quantity <= Product.min_stock_level,
        Product.is_active == True
    ).all()
    
    # أحدث الفواتير
    recent_invoices = Invoice.query.order_by(desc(Invoice.created_at)).limit(5).all()
    
    # المنتجات الأكثر مبيعاً (آخر 30 يوم)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    top_products = db.session.query(
        Product.name,
        func.sum(InvoiceItem.quantity).label('total_sold')
    ).join(InvoiceItem).join(Invoice).filter(
        Invoice.created_at >= thirty_days_ago,
        Invoice.payment_status == 'paid'
    ).group_by(Product.id).order_by(desc('total_sold')).limit(5).all()
    
    return render_template('dashboard.html',
                         today_sales=today_sales,
                         today_invoices=today_invoices,
                         month_sales=month_sales,
                         low_stock_products=low_stock_products,
                         recent_invoices=recent_invoices,
                         top_products=top_products)

# نقطة البيع
@app.route('/pos')
@login_required
def pos():
    categories = Category.query.filter_by(is_active=True).all()
    products = Product.query.filter_by(is_active=True).all()
    customers = Customer.query.filter_by(is_active=True).all()
    
    return render_template('pos/index.html',
                         categories=categories,
                         products=products,
                         customers=customers)

# البحث عن منتج (AJAX)
@app.route('/api/search_product')
@login_required
def search_product():
    query = request.args.get('q', '')
    barcode = request.args.get('barcode', '')
    
    if barcode:
        product = Product.query.filter_by(barcode=barcode, is_active=True).first()
        if product:
            return jsonify({
                'success': True,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'price': product.selling_price,
                    'stock': product.stock_quantity,
                    'barcode': product.barcode
                }
            })
    
    if query:
        products = Product.query.filter(
            Product.name.contains(query),
            Product.is_active == True
        ).limit(10).all()
        
        return jsonify({
            'success': True,
            'products': [{
                'id': p.id,
                'name': p.name,
                'price': p.selling_price,
                'stock': p.stock_quantity,
                'barcode': p.barcode
            } for p in products]
        })
    
    return jsonify({'success': False, 'message': 'لم يتم العثور على منتجات'})

# إنشاء فاتورة جديدة
@app.route('/api/create_invoice', methods=['POST'])
@login_required
def create_invoice():
    try:
        data = request.get_json()
        
        # إنشاء رقم فاتورة جديد
        last_invoice = Invoice.query.order_by(desc(Invoice.id)).first()
        invoice_number = f"INV-{(last_invoice.id + 1) if last_invoice else 1:06d}"
        
        # إنشاء الفاتورة
        invoice = Invoice(
            invoice_number=invoice_number,
            customer_id=data.get('customer_id'),
            user_id=current_user.id,
            subtotal=data['subtotal'],
            tax_amount=data['tax_amount'],
            discount_amount=data.get('discount_amount', 0),
            total_amount=data['total_amount'],
            payment_method=data['payment_method'],
            notes=data.get('notes', '')
        )
        
        db.session.add(invoice)
        db.session.flush()  # للحصول على ID الفاتورة
        
        # إضافة عناصر الفاتورة
        for item in data['items']:
            invoice_item = InvoiceItem(
                invoice_id=invoice.id,
                product_id=item['product_id'],
                quantity=item['quantity'],
                unit_price=item['unit_price'],
                total_price=item['total_price']
            )
            db.session.add(invoice_item)
            
            # تحديث المخزون
            product = Product.query.get(item['product_id'])
            if product:
                product.stock_quantity -= item['quantity']
                
                # تسجيل حركة المخزون
                stock_movement = StockMovement(
                    product_id=product.id,
                    movement_type='out',
                    quantity=item['quantity'],
                    reference_type='invoice',
                    reference_id=invoice.id,
                    user_id=current_user.id,
                    notes=f'بيع - فاتورة رقم {invoice_number}'
                )
                db.session.add(stock_movement)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'invoice_id': invoice.id,
            'invoice_number': invoice_number,
            'message': 'تم إنشاء الفاتورة بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })

# عرض الفاتورة
@app.route('/invoice/<int:invoice_id>')
@login_required
def view_invoice(invoice_id):
    invoice = Invoice.query.get_or_404(invoice_id)
    return render_template('invoices/view.html', invoice=invoice)

# طباعة الفاتورة
@app.route('/invoice/<int:invoice_id>/print')
@login_required
def print_invoice(invoice_id):
    invoice = Invoice.query.get_or_404(invoice_id)
    return render_template('invoices/print.html', invoice=invoice)

# إدارة المنتجات
@app.route('/products')
@login_required
def products():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)
    
    query = Product.query
    
    if search:
        query = query.filter(Product.name.contains(search))
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    products = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    categories = Category.query.filter_by(is_active=True).all()
    
    return render_template('products/index.html',
                         products=products,
                         categories=categories,
                         search=search,
                         selected_category=category_id)

# إضافة منتج جديد
@app.route('/products/add', methods=['GET', 'POST'])
@login_required
def add_product():
    if current_user.role not in ['admin', 'manager']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('products'))
    
    if request.method == 'POST':
        try:
            product = Product(
                name=request.form['name'],
                barcode=request.form.get('barcode'),
                description=request.form.get('description'),
                category_id=request.form.get('category_id'),
                purchase_price=float(request.form['purchase_price']),
                selling_price=float(request.form['selling_price']),
                stock_quantity=int(request.form['stock_quantity']),
                min_stock_level=int(request.form.get('min_stock_level', 5))
            )
            
            db.session.add(product)
            db.session.commit()
            
            flash('تم إضافة المنتج بنجاح', 'success')
            return redirect(url_for('products'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    categories = Category.query.filter_by(is_active=True).all()
    return render_template('products/add.html', categories=categories)
