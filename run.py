#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقاط البيع (POS System)
ملف التشغيل الرئيسي
"""

import os
import sys
from app import app, db
from models import User

def setup_database():
    """إعداد قاعدة البيانات وإنشاء الجداول"""
    with app.app_context():
        try:
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
            
            # التحقق من وجود مستخدم افتراضي
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                # إنشاء مستخدم افتراضي
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    role='admin'
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
            else:
                print("ℹ️  المستخدم الافتراضي موجود مسبقاً")
                
        except Exception as e:
            print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
            return False
    
    return True

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        import werkzeug
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("يرجى تشغيل: pip install -r requirements.txt")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام نقاط البيع")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # إعداد قاعدة البيانات
    if not setup_database():
        sys.exit(1)
    
    print("\n📋 معلومات النظام:")
    print(f"   🌐 العنوان: http://localhost:5000")
    print(f"   👤 المستخدم: admin")
    print(f"   🔑 كلمة المرور: admin123")
    print(f"   📁 قاعدة البيانات: {app.config['SQLALCHEMY_DATABASE_URI']}")
    
    print("\n🎯 المميزات المتاحة:")
    print("   ✅ نظام تسجيل الدخول والصلاحيات")
    print("   ✅ لوحة التحكم مع الإحصائيات")
    print("   ✅ نقطة البيع (POS) التفاعلية")
    print("   ✅ إدارة المنتجات والمخزون")
    print("   ✅ إدارة الفواتير والعملاء")
    print("   ✅ نظام التقارير الشامل")
    print("   ✅ دعم الباركود والطباعة")
    print("   ✅ إعدادات النظام المتقدمة")
    
    print("\n⌨️  اختصارات لوحة المفاتيح:")
    print("   Ctrl+N: بيع جديد")
    print("   F1: المساعدة")
    print("   F9: لوحة التحكم")
    print("   F10: نقطة البيع")
    
    print("\n" + "=" * 50)
    print("🎉 النظام جاهز للاستخدام!")
    print("=" * 50)
    
    try:
        # تشغيل التطبيق
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")

if __name__ == '__main__':
    main()
