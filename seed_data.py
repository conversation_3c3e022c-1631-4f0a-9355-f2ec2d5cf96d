from app import app, db
from models import User, Category, Product, Customer, SystemSettings
from werkzeug.security import generate_password_hash

def create_sample_data():
    """إنشاء بيانات تجريبية للنظام"""

    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # إنشاء المستخدمين
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
        
        if not User.query.filter_by(username='cashier').first():
            cashier = User(
                username='cashier',
                email='<EMAIL>',
                full_name='أحمد الكاشير',
                role='cashier'
            )
            cashier.set_password('cashier123')
            db.session.add(cashier)
        
        # إنشاء التصنيفات
        categories_data = [
            {'name': 'مشروبات', 'description': 'المشروبات الباردة والساخنة'},
            {'name': 'مأكولات', 'description': 'الوجبات والأطعمة'},
            {'name': 'حلويات', 'description': 'الحلويات والسكاكر'},
            {'name': 'مستلزمات', 'description': 'المستلزمات المنزلية'},
            {'name': 'إلكترونيات', 'description': 'الأجهزة الإلكترونية'}
        ]
        
        for cat_data in categories_data:
            if not Category.query.filter_by(name=cat_data['name']).first():
                category = Category(**cat_data)
                db.session.add(category)
        
        db.session.commit()
        
        # إنشاء المنتجات
        beverages_cat = Category.query.filter_by(name='مشروبات').first()
        food_cat = Category.query.filter_by(name='مأكولات').first()
        sweets_cat = Category.query.filter_by(name='حلويات').first()
        supplies_cat = Category.query.filter_by(name='مستلزمات').first()
        electronics_cat = Category.query.filter_by(name='إلكترونيات').first()
        
        products_data = [
            # مشروبات
            {'name': 'كوكا كولا', 'barcode': '1234567890', 'category_id': beverages_cat.id, 
             'purchase_price': 1.50, 'selling_price': 2.00, 'stock_quantity': 100, 'min_stock_level': 10},
            {'name': 'بيبسي', 'barcode': '1234567891', 'category_id': beverages_cat.id,
             'purchase_price': 1.50, 'selling_price': 2.00, 'stock_quantity': 80, 'min_stock_level': 10},
            {'name': 'ماء', 'barcode': '1234567892', 'category_id': beverages_cat.id,
             'purchase_price': 0.50, 'selling_price': 1.00, 'stock_quantity': 200, 'min_stock_level': 20},
            {'name': 'عصير برتقال', 'barcode': '1234567893', 'category_id': beverages_cat.id,
             'purchase_price': 2.00, 'selling_price': 3.00, 'stock_quantity': 50, 'min_stock_level': 5},
            {'name': 'قهوة', 'barcode': '1234567894', 'category_id': beverages_cat.id,
             'purchase_price': 3.00, 'selling_price': 5.00, 'stock_quantity': 30, 'min_stock_level': 5},
            
            # مأكولات
            {'name': 'ساندويش دجاج', 'barcode': '2234567890', 'category_id': food_cat.id,
             'purchase_price': 8.00, 'selling_price': 12.00, 'stock_quantity': 25, 'min_stock_level': 5},
            {'name': 'برجر لحم', 'barcode': '2234567891', 'category_id': food_cat.id,
             'purchase_price': 10.00, 'selling_price': 15.00, 'stock_quantity': 20, 'min_stock_level': 3},
            {'name': 'بيتزا مارجريتا', 'barcode': '2234567892', 'category_id': food_cat.id,
             'purchase_price': 15.00, 'selling_price': 25.00, 'stock_quantity': 15, 'min_stock_level': 3},
            {'name': 'سلطة خضراء', 'barcode': '2234567893', 'category_id': food_cat.id,
             'purchase_price': 5.00, 'selling_price': 8.00, 'stock_quantity': 30, 'min_stock_level': 5},
            
            # حلويات
            {'name': 'شوكولاتة', 'barcode': '3234567890', 'category_id': sweets_cat.id,
             'purchase_price': 2.00, 'selling_price': 3.50, 'stock_quantity': 60, 'min_stock_level': 10},
            {'name': 'كيك شوكولاتة', 'barcode': '3234567891', 'category_id': sweets_cat.id,
             'purchase_price': 12.00, 'selling_price': 20.00, 'stock_quantity': 10, 'min_stock_level': 2},
            {'name': 'آيس كريم', 'barcode': '3234567892', 'category_id': sweets_cat.id,
             'purchase_price': 3.00, 'selling_price': 5.00, 'stock_quantity': 40, 'min_stock_level': 5},
            
            # مستلزمات
            {'name': 'مناديل ورقية', 'barcode': '4234567890', 'category_id': supplies_cat.id,
             'purchase_price': 3.00, 'selling_price': 5.00, 'stock_quantity': 50, 'min_stock_level': 10},
            {'name': 'صابون', 'barcode': '4234567891', 'category_id': supplies_cat.id,
             'purchase_price': 4.00, 'selling_price': 7.00, 'stock_quantity': 30, 'min_stock_level': 5},
            {'name': 'شامبو', 'barcode': '4234567892', 'category_id': supplies_cat.id,
             'purchase_price': 8.00, 'selling_price': 12.00, 'stock_quantity': 25, 'min_stock_level': 5},
            
            # إلكترونيات
            {'name': 'سماعات', 'barcode': '5234567890', 'category_id': electronics_cat.id,
             'purchase_price': 25.00, 'selling_price': 40.00, 'stock_quantity': 15, 'min_stock_level': 3},
            {'name': 'كابل USB', 'barcode': '5234567891', 'category_id': electronics_cat.id,
             'purchase_price': 5.00, 'selling_price': 10.00, 'stock_quantity': 35, 'min_stock_level': 5},
            {'name': 'شاحن جوال', 'barcode': '5234567892', 'category_id': electronics_cat.id,
             'purchase_price': 15.00, 'selling_price': 25.00, 'stock_quantity': 20, 'min_stock_level': 3}
        ]
        
        for prod_data in products_data:
            if not Product.query.filter_by(barcode=prod_data['barcode']).first():
                product = Product(**prod_data)
                db.session.add(product)
        
        # إنشاء عملاء تجريبيين
        customers_data = [
            {'name': 'محمد أحمد', 'phone': '0501234567', 'email': '<EMAIL>'},
            {'name': 'فاطمة علي', 'phone': '0507654321', 'email': '<EMAIL>'},
            {'name': 'عبدالله سعد', 'phone': '0509876543', 'email': '<EMAIL>'},
            {'name': 'نورا خالد', 'phone': '0502468135', 'email': '<EMAIL>'},
            {'name': 'أحمد محمود', 'phone': '0508642097', 'email': '<EMAIL>'}
        ]
        
        for cust_data in customers_data:
            if not Customer.query.filter_by(phone=cust_data['phone']).first():
                customer = Customer(**cust_data)
                db.session.add(customer)
        
        # إعدادات النظام
        settings_data = [
            {'key': 'company_name', 'value': 'متجر الأمل', 'description': 'اسم الشركة'},
            {'key': 'company_address', 'value': 'الرياض، المملكة العربية السعودية', 'description': 'عنوان الشركة'},
            {'key': 'company_phone', 'value': '+966-11-123-4567', 'description': 'هاتف الشركة'},
            {'key': 'company_email', 'value': '<EMAIL>', 'description': 'بريد الشركة الإلكتروني'},
            {'key': 'tax_rate', 'value': '0.15', 'description': 'معدل الضريبة'},
            {'key': 'currency', 'value': 'ريال سعودي', 'description': 'العملة'},
            {'key': 'currency_symbol', 'value': 'ر.س', 'description': 'رمز العملة'},
            {'key': 'receipt_footer', 'value': 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى', 'description': 'تذييل الفاتورة'}
        ]
        
        for setting_data in settings_data:
            if not SystemSettings.query.filter_by(key=setting_data['key']).first():
                setting = SystemSettings(**setting_data)
                db.session.add(setting)
        
        db.session.commit()
        print("✅ تم إنشاء البيانات التجريبية بنجاح!")
        print("\n📋 بيانات تسجيل الدخول:")
        print("👤 المدير: admin / admin123")
        print("👤 الكاشير: cashier / cashier123")
        print(f"\n📊 تم إنشاء:")
        print(f"   - {len(categories_data)} تصنيفات")
        print(f"   - {len(products_data)} منتجات")
        print(f"   - {len(customers_data)} عملاء")
        print(f"   - {len(settings_data)} إعدادات")

if __name__ == '__main__':
    create_sample_data()
