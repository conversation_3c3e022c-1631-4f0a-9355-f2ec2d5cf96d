#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد البيانات التجريبية لنظام نقاط البيع
"""

from app import app, db
from models import User, Category, Product, Customer, SystemSettings, Invoice, InvoiceItem, StockMovement
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import random

def create_demo_data():
    """إنشاء بيانات تجريبية شاملة"""
    
    with app.app_context():
        print("🚀 بدء إنشاء البيانات التجريبية...")
        
        # إنشاء الجداول
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # إنشاء المستخدمين
        create_users()
        
        # إنشاء التصنيفات
        create_categories()
        
        # إنشاء المنتجات
        create_products()
        
        # إنشاء العملاء
        create_customers()
        
        # إنشاء فواتير تجريبية
        create_sample_invoices()
        
        # إنشاء إعدادات النظام
        create_system_settings()
        
        print("\n🎉 تم إنشاء البيانات التجريبية بنجاح!")
        print_summary()

def create_users():
    """إنشاء المستخدمين"""
    users_data = [
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'full_name': 'مدير النظام',
            'role': 'admin',
            'password': 'admin123'
        },
        {
            'username': 'cashier1',
            'email': '<EMAIL>',
            'full_name': 'أحمد الكاشير',
            'role': 'cashier',
            'password': 'cashier123'
        },
        {
            'username': 'cashier2',
            'email': '<EMAIL>',
            'full_name': 'فاطمة الكاشير',
            'role': 'cashier',
            'password': 'cashier123'
        },
        {
            'username': 'accountant',
            'email': '<EMAIL>',
            'full_name': 'محمد المحاسب',
            'role': 'accountant',
            'password': 'accountant123'
        }
    ]
    
    for user_data in users_data:
        if not User.query.filter_by(username=user_data['username']).first():
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                full_name=user_data['full_name'],
                role=user_data['role']
            )
            user.set_password(user_data['password'])
            db.session.add(user)
    
    db.session.commit()
    print("✅ تم إنشاء المستخدمين")

def create_categories():
    """إنشاء التصنيفات"""
    categories_data = [
        {'name': 'مشروبات باردة', 'description': 'المشروبات الباردة والعصائر'},
        {'name': 'مشروبات ساخنة', 'description': 'القهوة والشاي والمشروبات الساخنة'},
        {'name': 'وجبات سريعة', 'description': 'البرجر والساندويش والوجبات السريعة'},
        {'name': 'حلويات', 'description': 'الكيك والحلويات والآيس كريم'},
        {'name': 'مخبوزات', 'description': 'الخبز والمعجنات والفطائر'},
        {'name': 'منتجات ألبان', 'description': 'الحليب والجبن واللبن'},
        {'name': 'مستلزمات منزلية', 'description': 'المنظفات والمستلزمات المنزلية'},
        {'name': 'إلكترونيات', 'description': 'الأجهزة الإلكترونية والإكسسوارات'}
    ]
    
    for cat_data in categories_data:
        if not Category.query.filter_by(name=cat_data['name']).first():
            category = Category(**cat_data)
            db.session.add(category)
    
    db.session.commit()
    print("✅ تم إنشاء التصنيفات")

def create_products():
    """إنشاء المنتجات"""
    categories = Category.query.all()
    
    products_data = [
        # مشروبات باردة
        {'name': 'كوكا كولا 330مل', 'barcode': '1001', 'category': 'مشروبات باردة', 'purchase_price': 1.50, 'selling_price': 2.50, 'stock': 100},
        {'name': 'بيبسي 330مل', 'barcode': '1002', 'category': 'مشروبات باردة', 'purchase_price': 1.50, 'selling_price': 2.50, 'stock': 80},
        {'name': 'ماء صافي 500مل', 'barcode': '1003', 'category': 'مشروبات باردة', 'purchase_price': 0.50, 'selling_price': 1.00, 'stock': 200},
        {'name': 'عصير برتقال طبيعي', 'barcode': '1004', 'category': 'مشروبات باردة', 'purchase_price': 3.00, 'selling_price': 5.00, 'stock': 50},
        {'name': 'عصير تفاح', 'barcode': '1005', 'category': 'مشروبات باردة', 'purchase_price': 2.50, 'selling_price': 4.00, 'stock': 60},
        
        # مشروبات ساخنة
        {'name': 'قهوة عربية', 'barcode': '2001', 'category': 'مشروبات ساخنة', 'purchase_price': 2.00, 'selling_price': 5.00, 'stock': 30},
        {'name': 'كابتشينو', 'barcode': '2002', 'category': 'مشروبات ساخنة', 'purchase_price': 3.00, 'selling_price': 8.00, 'stock': 25},
        {'name': 'شاي أحمر', 'barcode': '2003', 'category': 'مشروبات ساخنة', 'purchase_price': 1.00, 'selling_price': 3.00, 'stock': 40},
        {'name': 'شاي أخضر', 'barcode': '2004', 'category': 'مشروبات ساخنة', 'purchase_price': 1.50, 'selling_price': 4.00, 'stock': 35},
        
        # وجبات سريعة
        {'name': 'برجر دجاج', 'barcode': '3001', 'category': 'وجبات سريعة', 'purchase_price': 8.00, 'selling_price': 15.00, 'stock': 20},
        {'name': 'برجر لحم', 'barcode': '3002', 'category': 'وجبات سريعة', 'purchase_price': 10.00, 'selling_price': 18.00, 'stock': 15},
        {'name': 'ساندويش تونة', 'barcode': '3003', 'category': 'وجبات سريعة', 'purchase_price': 5.00, 'selling_price': 10.00, 'stock': 25},
        {'name': 'بيتزا مارجريتا', 'barcode': '3004', 'category': 'وجبات سريعة', 'purchase_price': 12.00, 'selling_price': 25.00, 'stock': 10},
        
        # حلويات
        {'name': 'كيك شوكولاتة', 'barcode': '4001', 'category': 'حلويات', 'purchase_price': 15.00, 'selling_price': 30.00, 'stock': 8},
        {'name': 'آيس كريم فانيليا', 'barcode': '4002', 'category': 'حلويات', 'purchase_price': 3.00, 'selling_price': 7.00, 'stock': 40},
        {'name': 'دونات محشي', 'barcode': '4003', 'category': 'حلويات', 'purchase_price': 2.00, 'selling_price': 5.00, 'stock': 30},
        
        # مخبوزات
        {'name': 'خبز عربي', 'barcode': '5001', 'category': 'مخبوزات', 'purchase_price': 1.00, 'selling_price': 2.00, 'stock': 50},
        {'name': 'كرواسان', 'barcode': '5002', 'category': 'مخبوزات', 'purchase_price': 1.50, 'selling_price': 3.50, 'stock': 25},
        
        # منتجات ألبان
        {'name': 'حليب طازج 1 لتر', 'barcode': '6001', 'category': 'منتجات ألبان', 'purchase_price': 4.00, 'selling_price': 6.00, 'stock': 30},
        {'name': 'جبن أبيض', 'barcode': '6002', 'category': 'منتجات ألبان', 'purchase_price': 8.00, 'selling_price': 12.00, 'stock': 20},
        
        # مستلزمات منزلية
        {'name': 'مناديل ورقية', 'barcode': '7001', 'category': 'مستلزمات منزلية', 'purchase_price': 3.00, 'selling_price': 6.00, 'stock': 40},
        {'name': 'صابون سائل', 'barcode': '7002', 'category': 'مستلزمات منزلية', 'purchase_price': 5.00, 'selling_price': 9.00, 'stock': 25},
        
        # إلكترونيات
        {'name': 'سماعات أذن', 'barcode': '8001', 'category': 'إلكترونيات', 'purchase_price': 25.00, 'selling_price': 45.00, 'stock': 15},
        {'name': 'كابل USB', 'barcode': '8002', 'category': 'إلكترونيات', 'purchase_price': 8.00, 'selling_price': 15.00, 'stock': 30}
    ]
    
    category_map = {cat.name: cat.id for cat in categories}
    
    for prod_data in products_data:
        if not Product.query.filter_by(barcode=prod_data['barcode']).first():
            product = Product(
                name=prod_data['name'],
                barcode=prod_data['barcode'],
                category_id=category_map.get(prod_data['category']),
                purchase_price=prod_data['purchase_price'],
                selling_price=prod_data['selling_price'],
                stock_quantity=prod_data['stock'],
                min_stock_level=5
            )
            db.session.add(product)
    
    db.session.commit()
    print("✅ تم إنشاء المنتجات")

def create_customers():
    """إنشاء العملاء"""
    customers_data = [
        {'name': 'محمد أحمد السعيد', 'phone': '0501234567', 'email': '<EMAIL>'},
        {'name': 'فاطمة علي الزهراني', 'phone': '0507654321', 'email': '<EMAIL>'},
        {'name': 'عبدالله سعد القحطاني', 'phone': '0509876543', 'email': '<EMAIL>'},
        {'name': 'نورا خالد العتيبي', 'phone': '0502468135', 'email': '<EMAIL>'},
        {'name': 'أحمد محمود الغامدي', 'phone': '0508642097', 'email': '<EMAIL>'},
        {'name': 'سارة عبدالرحمن', 'phone': '0505551234', 'email': '<EMAIL>'},
        {'name': 'يوسف إبراهيم', 'phone': '0506667890', 'email': '<EMAIL>'},
        {'name': 'مريم حسن', 'phone': '0507778901', 'email': '<EMAIL>'}
    ]
    
    for cust_data in customers_data:
        if not Customer.query.filter_by(phone=cust_data['phone']).first():
            customer = Customer(**cust_data)
            db.session.add(customer)
    
    db.session.commit()
    print("✅ تم إنشاء العملاء")

def create_sample_invoices():
    """إنشاء فواتير تجريبية"""
    users = User.query.filter_by(role='cashier').all()
    customers = Customer.query.all()
    products = Product.query.all()
    
    if not users:
        users = [User.query.filter_by(role='admin').first()]
    
    # إنشاء فواتير للأيام الماضية
    for i in range(30):  # آخر 30 يوم
        date = datetime.now() - timedelta(days=i)
        num_invoices = random.randint(2, 8)  # 2-8 فواتير يومياً
        
        for j in range(num_invoices):
            create_random_invoice(users, customers, products, date)
    
    db.session.commit()
    print("✅ تم إنشاء الفواتير التجريبية")

def create_random_invoice(users, customers, products, date):
    """إنشاء فاتورة عشوائية"""
    user = random.choice(users)
    customer = random.choice(customers) if random.random() > 0.3 else None  # 70% عملاء مسجلين
    
    # إنشاء رقم فاتورة
    last_invoice = Invoice.query.order_by(Invoice.id.desc()).first()
    invoice_number = f"INV-{(last_invoice.id + 1) if last_invoice else 1:06d}"
    
    # اختيار منتجات عشوائية
    selected_products = random.sample(products, random.randint(1, 5))
    
    subtotal = 0
    invoice = Invoice(
        invoice_number=invoice_number,
        customer_id=customer.id if customer else None,
        user_id=user.id,
        payment_method=random.choice(['cash', 'card', 'credit']),
        created_at=date
    )
    
    db.session.add(invoice)
    db.session.flush()
    
    # إضافة عناصر الفاتورة
    for product in selected_products:
        quantity = random.randint(1, 3)
        unit_price = product.selling_price
        total_price = quantity * unit_price
        subtotal += total_price
        
        item = InvoiceItem(
            invoice_id=invoice.id,
            product_id=product.id,
            quantity=quantity,
            unit_price=unit_price,
            total_price=total_price
        )
        db.session.add(item)
    
    # حساب الضريبة والإجمالي
    discount = subtotal * random.uniform(0, 0.1)  # خصم عشوائي 0-10%
    taxable_amount = subtotal - discount
    tax_amount = taxable_amount * 0.15
    total_amount = taxable_amount + tax_amount
    
    invoice.subtotal = subtotal
    invoice.discount_amount = discount
    invoice.tax_amount = tax_amount
    invoice.total_amount = total_amount

def create_system_settings():
    """إنشاء إعدادات النظام"""
    settings_data = [
        {'key': 'company_name', 'value': 'متجر الأمل التجاري', 'description': 'اسم الشركة'},
        {'key': 'company_address', 'value': 'شارع الملك فهد، الرياض 12345، المملكة العربية السعودية', 'description': 'عنوان الشركة'},
        {'key': 'company_phone', 'value': '+966-11-123-4567', 'description': 'هاتف الشركة'},
        {'key': 'company_email', 'value': '<EMAIL>', 'description': 'بريد الشركة الإلكتروني'},
        {'key': 'tax_number', 'value': '123456789012345', 'description': 'الرقم الضريبي'},
        {'key': 'tax_rate', 'value': '0.15', 'description': 'معدل الضريبة'},
        {'key': 'currency', 'value': 'ريال سعودي', 'description': 'العملة'},
        {'key': 'currency_symbol', 'value': 'ر.س', 'description': 'رمز العملة'},
        {'key': 'receipt_header', 'value': 'مرحباً بكم في متجر الأمل', 'description': 'رأس الفاتورة'},
        {'key': 'receipt_footer', 'value': 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى', 'description': 'تذييل الفاتورة'},
        {'key': 'low_stock_threshold', 'value': '5', 'description': 'حد التنبيه للمخزون المنخفض'}
    ]
    
    for setting_data in settings_data:
        if not SystemSettings.query.filter_by(key=setting_data['key']).first():
            setting = SystemSettings(**setting_data)
            db.session.add(setting)
    
    db.session.commit()
    print("✅ تم إنشاء إعدادات النظام")

def print_summary():
    """طباعة ملخص البيانات المنشأة"""
    with app.app_context():
        users_count = User.query.count()
        categories_count = Category.query.count()
        products_count = Product.query.count()
        customers_count = Customer.query.count()
        invoices_count = Invoice.query.count()
        settings_count = SystemSettings.query.count()
        
        print("\n📊 ملخص البيانات التجريبية:")
        print(f"   👥 المستخدمون: {users_count}")
        print(f"   📂 التصنيفات: {categories_count}")
        print(f"   📦 المنتجات: {products_count}")
        print(f"   👤 العملاء: {customers_count}")
        print(f"   🧾 الفواتير: {invoices_count}")
        print(f"   ⚙️  الإعدادات: {settings_count}")
        
        print("\n🔑 بيانات تسجيل الدخول:")
        print("   👤 المدير: admin / admin123")
        print("   👤 الكاشير 1: cashier1 / cashier123")
        print("   👤 الكاشير 2: cashier2 / cashier123")
        print("   👤 المحاسب: accountant / accountant123")

if __name__ == '__main__':
    create_demo_data()
