#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقاط البيع المبسط
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///simple_pos.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='cashier')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    price = db.Column(db.Float, nullable=False)
    stock = db.Column(db.Integer, nullable=False, default=0)

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_name = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    total = db.Column(db.Float, nullable=False)
    date = db.Column(db.DateTime, default=datetime.now)

# قوالب HTML
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            font-family: 'Arial', sans-serif;
        }
        .card { border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
    </style>
</head>
<body>
    <div class="container d-flex align-items-center justify-content-center min-vh-100">
        <div class="card" style="width: 100%; max-width: 400px;">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-cash-register fa-3x text-primary mb-3"></i>
                    <h3>نظام نقاط البيع</h3>
                    <p class="text-muted">يرجى تسجيل الدخول</p>
                </div>
                
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-danger">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">تسجيل الدخول</button>
                    </div>
                </form>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        المستخدم: <strong>admin</strong><br>
                        كلمة المرور: <strong>admin123</strong>
                    </small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; font-family: 'Arial', sans-serif; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card { border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }
        .stats-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark">
        <div class="container">
            <span class="navbar-brand">
                <i class="fas fa-cash-register me-2"></i>
                نظام نقاط البيع
            </span>
            <div>
                <span class="text-white me-3">مرحباً، {{ session.user_name }}</span>
                <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-box fa-2x mb-2"></i>
                        <h4>{{ products_count }}</h4>
                        <p>المنتجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h4>{{ sales_count }}</h4>
                        <p>المبيعات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>{{ total_sales }}</h4>
                        <p>إجمالي المبيعات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar fa-2x mb-2"></i>
                        <h4>اليوم</h4>
                        <p>{{ today_date }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-plus me-2"></i>إضافة منتج</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('add_product') }}">
                            <div class="mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">السعر</label>
                                <input type="number" class="form-control" name="price" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="stock" required>
                            </div>
                            <button type="submit" class="btn btn-primary">إضافة</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-shopping-cart me-2"></i>بيع منتج</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('make_sale') }}">
                            <div class="mb-3">
                                <label class="form-label">المنتج</label>
                                <select class="form-select" name="product_id" required>
                                    <option value="">اختر منتج</option>
                                    {% for product in products %}
                                    <option value="{{ product.id }}">{{ product.name }} - {{ product.price }} ر.س</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" min="1" required>
                            </div>
                            <button type="submit" class="btn btn-success">بيع</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-list me-2"></i>المنتجات</h5>
                    </div>
                    <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                        {% for product in products %}
                        <div class="border-bottom pb-2 mb-2">
                            <strong>{{ product.name }}</strong><br>
                            <small>السعر: {{ product.price }} ر.س | المخزون: {{ product.stock }}</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-history me-2"></i>آخر المبيعات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>الإجمالي</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sale in recent_sales %}
                                    <tr>
                                        <td>{{ sale.product_name }}</td>
                                        <td>{{ sale.quantity }}</td>
                                        <td>{{ sale.total }} ر.س</td>
                                        <td>{{ sale.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

# المسارات
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            session['user_id'] = user.id
            session['user_name'] = user.full_name
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    products = Product.query.all()
    sales = Sale.query.all()
    recent_sales = Sale.query.order_by(Sale.date.desc()).limit(10).all()
    
    products_count = len(products)
    sales_count = len(sales)
    total_sales = sum(sale.total for sale in sales)
    today_date = datetime.now().strftime('%Y-%m-%d')
    
    return render_template_string(DASHBOARD_TEMPLATE,
                                products=products,
                                recent_sales=recent_sales,
                                products_count=products_count,
                                sales_count=sales_count,
                                total_sales=f"{total_sales:.2f}",
                                today_date=today_date)

@app.route('/add_product', methods=['POST'])
def add_product():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    name = request.form['name']
    price = float(request.form['price'])
    stock = int(request.form['stock'])
    
    product = Product(name=name, price=price, stock=stock)
    db.session.add(product)
    db.session.commit()
    
    return redirect(url_for('dashboard'))

@app.route('/make_sale', methods=['POST'])
def make_sale():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    product_id = int(request.form['product_id'])
    quantity = int(request.form['quantity'])
    
    product = Product.query.get(product_id)
    if product and product.stock >= quantity:
        total = product.price * quantity
        
        sale = Sale(product_name=product.name, quantity=quantity, total=total)
        db.session.add(sale)
        
        product.stock -= quantity
        db.session.commit()
    
    return redirect(url_for('dashboard'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(username='admin', full_name='مدير النظام', role='admin')
            admin.set_password('admin123')
            db.session.add(admin)
            
            # إضافة منتجات تجريبية
            products = [
                Product(name='كوكا كولا', price=2.50, stock=100),
                Product(name='بيبسي', price=2.50, stock=80),
                Product(name='ماء', price=1.00, stock=200),
                Product(name='عصير برتقال', price=5.00, stock=50),
                Product(name='قهوة', price=8.00, stock=30)
            ]
            
            for product in products:
                db.session.add(product)
            
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي والمنتجات التجريبية")
    
    print("🚀 نظام نقاط البيع المبسط")
    print("🌐 العنوان: http://localhost:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
