#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام نقاط البيع
"""

import os
import sys

def main():
    """تشغيل النظام"""
    print("🚀 نظام نقاط البيع (POS System)")
    print("=" * 50)
    
    print("📋 معلومات النظام:")
    print("   🌐 العنوان: http://localhost:5000")
    print("   👤 المستخدم: admin")
    print("   🔑 كلمة المرور: admin123")
    
    print("\n🎯 المميزات المتاحة:")
    print("   ✅ نظام تسجيل الدخول والصلاحيات")
    print("   ✅ لوحة التحكم مع الإحصائيات")
    print("   ✅ نقطة البيع (POS) التفاعلية")
    print("   ✅ إدارة المنتجات والمخزون")
    print("   ✅ إدارة الفواتير والعملاء")
    print("   ✅ نظام التقارير الشامل")
    print("   ✅ دعم الباركود والطباعة")
    print("   ✅ إعدادات النظام المتقدمة")
    
    print("\n⌨️  اختصارات لوحة المفاتيح:")
    print("   Ctrl+N: بيع جديد")
    print("   F1: المساعدة")
    print("   F9: لوحة التحكم")
    print("   F10: نقطة البيع")
    
    print("\n" + "=" * 50)
    print("🎉 النظام جاهز للاستخدام!")
    print("=" * 50)
    
    # تشغيل التطبيق
    os.system("python app.py")

if __name__ == '__main__':
    main()
