// Custom JavaScript for POS System

// Global variables
let currentUser = null;
let systemSettings = {};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
    setupEventListeners();
    loadUserPreferences();
});

// System initialization
function initializeSystem() {
    // Add loading animation to cards
    animateCards();
    
    // Setup tooltips
    setupTooltips();
    
    // Setup auto-refresh for dashboard
    if (window.location.pathname === '/dashboard') {
        setupDashboardRefresh();
    }
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
}

// Animate cards on page load
function animateCards() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Setup tooltips
function setupTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Setup dashboard auto-refresh
function setupDashboardRefresh() {
    // Refresh dashboard every 5 minutes
    setInterval(() => {
        if (document.visibilityState === 'visible') {
            refreshDashboardStats();
        }
    }, 300000);
}

// Setup keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'n': // New sale
                    e.preventDefault();
                    if (window.location.pathname !== '/pos') {
                        window.location.href = '/pos';
                    }
                    break;
                case 'p': // Print (handled in specific pages)
                    // Let individual pages handle this
                    break;
                case 's': // Save (prevent default browser save)
                    e.preventDefault();
                    break;
            }
        }
        
        // Function keys
        switch(e.key) {
            case 'F1': // Help
                e.preventDefault();
                showHelp();
                break;
            case 'F2': // Quick search
                e.preventDefault();
                focusSearch();
                break;
            case 'F9': // Dashboard
                e.preventDefault();
                window.location.href = '/dashboard';
                break;
            case 'F10': // POS
                e.preventDefault();
                window.location.href = '/pos';
                break;
        }
    });
}

// Setup event listeners
function setupEventListeners() {
    // Mobile sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }
    
    // Auto-hide alerts
    setupAutoHideAlerts();
    
    // Form validation enhancements
    setupFormValidation();
    
    // Search enhancements
    setupSearchEnhancements();
}

// Toggle mobile sidebar
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}

// Auto-hide alerts after 5 seconds
function setupAutoHideAlerts() {
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s ease';
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 5000);
    });
}

// Enhanced form validation
function setupFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    });
}

// Validate individual field
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // Email validation
    if (field.type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        message = 'يرجى إدخال بريد إلكتروني صحيح';
    }
    
    // Phone validation
    if (field.type === 'tel' && value && !isValidPhone(value)) {
        isValid = false;
        message = 'يرجى إدخال رقم جوال صحيح';
    }
    
    // Number validation
    if (field.type === 'number' && value) {
        const num = parseFloat(value);
        const min = parseFloat(field.getAttribute('min'));
        const max = parseFloat(field.getAttribute('max'));
        
        if (isNaN(num)) {
            isValid = false;
            message = 'يرجى إدخال رقم صحيح';
        } else if (!isNaN(min) && num < min) {
            isValid = false;
            message = `القيمة يجب أن تكون أكبر من أو تساوي ${min}`;
        } else if (!isNaN(max) && num > max) {
            isValid = false;
            message = `القيمة يجب أن تكون أقل من أو تساوي ${max}`;
        }
    }
    
    // Update field appearance
    updateFieldValidation(field, isValid, message);
    
    return isValid;
}

// Update field validation appearance
function updateFieldValidation(field, isValid, message) {
    field.classList.remove('is-valid', 'is-invalid');
    
    // Remove existing feedback
    const existingFeedback = field.parentNode.querySelector('.invalid-feedback, .valid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    if (field.value.trim()) {
        if (isValid) {
            field.classList.add('is-valid');
        } else {
            field.classList.add('is-invalid');
            
            // Add error message
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = message;
            field.parentNode.appendChild(feedback);
        }
    }
}

// Validate entire form
function validateForm(form) {
    const fields = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    fields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Phone validation (Saudi format)
function isValidPhone(phone) {
    const phoneRegex = /^(05|5)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
}

// Search enhancements
function setupSearchEnhancements() {
    const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="بحث"]');
    searchInputs.forEach(input => {
        let searchTimeout;
        
        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this);
            }, 300);
        });
    });
}

// Perform search
function performSearch(input) {
    const query = input.value.trim();
    if (query.length >= 2) {
        // Add search logic here
        console.log('Searching for:', query);
    }
}

// Focus search input
function focusSearch() {
    const searchInput = document.querySelector('input[type="search"], input[placeholder*="بحث"]');
    if (searchInput) {
        searchInput.focus();
        searchInput.select();
    }
}

// Show help modal
function showHelp() {
    const helpModal = new bootstrap.Modal(document.getElementById('helpModal') || createHelpModal());
    helpModal.show();
}

// Create help modal
function createHelpModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'helpModal';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">المساعدة - اختصارات لوحة المفاتيح</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table class="table">
                        <tr><td><kbd>Ctrl + N</kbd></td><td>بيع جديد</td></tr>
                        <tr><td><kbd>Ctrl + P</kbd></td><td>طباعة</td></tr>
                        <tr><td><kbd>F1</kbd></td><td>المساعدة</td></tr>
                        <tr><td><kbd>F2</kbd></td><td>البحث السريع</td></tr>
                        <tr><td><kbd>F9</kbd></td><td>لوحة التحكم</td></tr>
                        <tr><td><kbd>F10</kbd></td><td>نقطة البيع</td></tr>
                    </table>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    return modal;
}

// Refresh dashboard statistics
function refreshDashboardStats() {
    fetch('/api/dashboard_stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboardStats(data.stats);
            }
        })
        .catch(error => {
            console.error('Error refreshing dashboard:', error);
        });
}

// Update dashboard statistics
function updateDashboardStats(stats) {
    // Update stats cards with animation
    Object.keys(stats).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            animateNumber(element, parseFloat(element.textContent), stats[key]);
        }
    });
}

// Animate number changes
function animateNumber(element, from, to) {
    const duration = 1000;
    const steps = 60;
    const stepValue = (to - from) / steps;
    let current = from;
    let step = 0;
    
    const timer = setInterval(() => {
        current += stepValue;
        step++;
        
        if (step >= steps) {
            current = to;
            clearInterval(timer);
        }
        
        element.textContent = current.toFixed(2);
    }, duration / steps);
}

// Load user preferences
function loadUserPreferences() {
    const preferences = localStorage.getItem('userPreferences');
    if (preferences) {
        try {
            const prefs = JSON.parse(preferences);
            applyUserPreferences(prefs);
        } catch (e) {
            console.error('Error loading user preferences:', e);
        }
    }
}

// Apply user preferences
function applyUserPreferences(preferences) {
    // Apply theme, language, etc.
    if (preferences.theme) {
        document.body.setAttribute('data-theme', preferences.theme);
    }
}

// Save user preferences
function saveUserPreferences(preferences) {
    localStorage.setItem('userPreferences', JSON.stringify(preferences));
}

// Utility functions
const Utils = {
    // Format currency
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    },
    
    // Format date
    formatDate: function(date) {
        return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    },
    
    // Confirm dialog
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
};

// Export for global use
window.POS = {
    Utils,
    showHelp,
    toggleSidebar,
    saveUserPreferences,
    loadUserPreferences
};
