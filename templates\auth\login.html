{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام نقاط البيع{% endblock %}

{% block auth_content %}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Login Form -->
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg border-0" style="width: 100%; max-width: 400px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-cash-register fa-3x text-primary mb-3"></i>
                        <h3 class="card-title fw-bold">نظام نقاط البيع</h3>
                        <p class="text-muted">يرجى تسجيل الدخول للمتابعة</p>
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>
                                اسم المستخدم
                            </label>
                            <input type="text" class="form-control form-control-lg" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>
                                كلمة المرور
                            </label>
                            <input type="password" class="form-control form-control-lg" id="password" name="password" required>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4 text-center">
                        <small class="text-muted">
                            المستخدم الافتراضي: <strong>admin</strong><br>
                            كلمة المرور: <strong>admin123</strong>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right side - Background -->
        <div class="col-md-6 d-none d-md-block position-relative">
            <div class="h-100 d-flex align-items-center justify-content-center" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="text-center text-white">
                    <i class="fas fa-store fa-5x mb-4"></i>
                    <h2 class="fw-bold mb-3">نظام إدارة المبيعات</h2>
                    <p class="lead">
                        نظام شامل لإدارة المبيعات والمخزون<br>
                        مع واجهة سهلة الاستخدام
                    </p>
                    <div class="row mt-5">
                        <div class="col-4">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <p>تقارير مفصلة</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-barcode fa-2x mb-2"></i>
                            <p>دعم الباركود</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-print fa-2x mb-2"></i>
                            <p>طباعة الفواتير</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .vh-100 {
        min-height: 100vh;
    }
    
    .card {
        border-radius: 20px;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        font-weight: 600;
        transition: transform 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Focus on username field when page loads
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('username').focus();
    });
    
    // Handle form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();
        
        if (!username || !password) {
            e.preventDefault();
            alert('يرجى إدخال اسم المستخدم وكلمة المرور');
        }
    });
</script>
{% endblock %}
