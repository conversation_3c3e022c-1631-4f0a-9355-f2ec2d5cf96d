{% extends "base.html" %}

{% block title %}إضافة عميل جديد - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة عميل جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للعملاء
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات العميل</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم العميل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الجوال</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="05xxxxxxxx">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="credit_balance" class="form-label">الرصيد الآجل</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="credit_balance" 
                                       name="credit_balance" value="0" step="0.01">
                                <span class="input-group-text">ر.س</span>
                            </div>
                            <small class="form-text text-muted">القيمة الموجبة تعني دين على العميل</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="العنوان التفصيلي للعميل..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" 
                                   name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                عميل نشط
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('customers') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح مفيدة:</h6>
                    <ul class="mb-0">
                        <li>اسم العميل مطلوب وضروري</li>
                        <li>رقم الجوال يساعد في التواصل</li>
                        <li>البريد الإلكتروني للإشعارات</li>
                        <li>الرصيد الآجل لتتبع المديونية</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                    <p class="mb-0">تأكد من صحة البيانات قبل الحفظ، خاصة رقم الجوال والبريد الإلكتروني.</p>
                </div>
            </div>
        </div>
        
        <!-- Customer Statistics Preview -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    معاينة الإحصائيات
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-primary">0</h5>
                        <small class="text-muted">عدد المشتريات</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success">0.00 ر.س</h5>
                        <small class="text-muted">إجمالي المشتريات</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <small class="text-muted">ستظهر الإحصائيات بعد أول عملية شراء</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        
        // Saudi phone number format
        if (value.startsWith('966')) {
            value = value.substring(3);
        }
        if (value.startsWith('0')) {
            value = value.substring(1);
        }
        if (value.length > 0 && !value.startsWith('5')) {
            value = '5' + value;
        }
        if (value.length > 9) {
            value = value.substring(0, 9);
        }
        if (value.length > 0) {
            value = '0' + value;
        }
        
        this.value = value;
    });
    
    // Email validation
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        if (this.value && !this.value.includes('@')) {
            this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
        } else {
            this.setCustomValidity('');
        }
    });
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const phone = document.getElementById('phone').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (!name) {
        alert('يرجى إدخال اسم العميل');
        e.preventDefault();
        return;
    }
    
    if (phone && phone.length < 10) {
        alert('رقم الجوال غير صحيح');
        e.preventDefault();
        return;
    }
    
    if (email && !email.includes('@')) {
        alert('البريد الإلكتروني غير صحيح');
        e.preventDefault();
        return;
    }
});
</script>
{% endblock %}
