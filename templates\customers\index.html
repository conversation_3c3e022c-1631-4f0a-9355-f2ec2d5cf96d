{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة العملاء
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة عميل جديد
            </a>
        </div>
    </div>
</div>

<!-- Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="اسم العميل أو رقم الجوال...">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة العملاء</h5>
    </div>
    <div class="card-body">
        {% if customers.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم العميل</th>
                        <th>رقم الجوال</th>
                        <th>البريد الإلكتروني</th>
                        <th>الرصيد الآجل</th>
                        <th>عدد المشتريات</th>
                        <th>تاريخ التسجيل</th>
                        <th>الحالة</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers.items %}
                    <tr>
                        <td>
                            <strong>{{ customer.name }}</strong>
                            {% if customer.address %}
                            <br><small class="text-muted">{{ customer.address[:30] }}...</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.phone %}
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    {{ customer.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.email %}
                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                    {{ customer.email }}
                                </a>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.credit_balance > 0 %}
                                <span class="text-danger fw-bold">
                                    {{ "%.2f"|format(customer.credit_balance) }} ر.س
                                </span>
                            {% elif customer.credit_balance < 0 %}
                                <span class="text-success fw-bold">
                                    +{{ "%.2f"|format(-customer.credit_balance) }} ر.س
                                </span>
                            {% else %}
                                <span class="text-muted">0.00 ر.س</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ customer.invoices|length }}</span>
                        </td>
                        <td>
                            {{ customer.created_at.strftime('%Y-%m-%d') }}
                        </td>
                        <td>
                            {% if customer.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('edit_customer', customer_id=customer.id) }}" 
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-info" title="عرض التفاصيل"
                                        onclick="showCustomerDetails({{ customer.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="{{ url_for('invoices') }}?customer={{ customer.id }}" 
                                   class="btn btn-outline-secondary" title="الفواتير">
                                    <i class="fas fa-file-invoice"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if customers.pages > 1 %}
        <nav aria-label="Customer pagination">
            <ul class="pagination justify-content-center">
                {% if customers.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('customers', page=customers.prev_num, search=search) }}">
                        السابق
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in customers.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != customers.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=page_num, search=search) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if customers.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('customers', page=customers.next_num, search=search) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد عملاء</h5>
            <p class="text-muted">لم يتم العثور على عملاء يطابقون معايير البحث</p>
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة عميل جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Customer Details Modal -->
<div class="modal fade" id="customerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="customerDetails">
                <!-- Customer details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showCustomerDetails(customerId) {
    document.getElementById('customerDetails').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">جاري تحميل التفاصيل...</p>
        </div>
    `;
    
    $('#customerModal').modal('show');
    
    // Here you would typically fetch customer details via AJAX
    setTimeout(() => {
        document.getElementById('customerDetails').innerHTML = `
            <p>تفاصيل العميل رقم ${customerId} ستظهر هنا</p>
            <p>يمكن إضافة المزيد من التفاصيل مثل:</p>
            <ul>
                <li>سجل المشتريات</li>
                <li>إجمالي المبالغ المشتراة</li>
                <li>آخر عملية شراء</li>
                <li>المنتجات المفضلة</li>
            </ul>
        `;
    }, 1000);
}
</script>
{% endblock %}
