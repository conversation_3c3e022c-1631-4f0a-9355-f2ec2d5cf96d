{% extends "base.html" %}

{% block title %}إدارة الفواتير - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-invoice me-2"></i>
        إدارة الفواتير
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('pos') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                فاتورة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="رقم الفاتورة أو اسم العميل...">
            </div>
            <div class="col-md-3">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="start_date" name="start_date">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="end_date" name="end_date">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة الفواتير</h5>
    </div>
    <div class="card-body">
        {% if invoices.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>الكاشير</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                        <th>التاريخ</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices.items %}
                    <tr>
                        <td>
                            <strong>{{ invoice.invoice_number }}</strong>
                        </td>
                        <td>
                            {% if invoice.customer %}
                                {{ invoice.customer.name }}
                                {% if invoice.customer.phone %}
                                    <br><small class="text-muted">{{ invoice.customer.phone }}</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">عميل نقدي</span>
                            {% endif %}
                        </td>
                        <td>{{ invoice.user.full_name }}</td>
                        <td>
                            <strong class="text-success">
                                {{ "%.2f"|format(invoice.total_amount) }} ر.س
                            </strong>
                        </td>
                        <td>
                            {% if invoice.payment_method == 'cash' %}
                                <span class="badge bg-success">نقدي</span>
                            {% elif invoice.payment_method == 'card' %}
                                <span class="badge bg-info">بطاقة</span>
                            {% else %}
                                <span class="badge bg-warning">آجل</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.payment_status == 'paid' %}
                                <span class="badge bg-success">مدفوع</span>
                            {% elif invoice.payment_status == 'pending' %}
                                <span class="badge bg-warning">معلق</span>
                            {% else %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ invoice.created_at.strftime('%Y-%m-%d') }}<br>
                            <small class="text-muted">{{ invoice.created_at.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('view_invoice', invoice_id=invoice.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('print_invoice', invoice_id=invoice.id) }}" 
                                   class="btn btn-outline-secondary" title="طباعة" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if invoices.pages > 1 %}
        <nav aria-label="Invoice pagination">
            <ul class="pagination justify-content-center">
                {% if invoices.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('invoices', page=invoices.prev_num, search=search) }}">
                        السابق
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in invoices.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != invoices.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('invoices', page=page_num, search=search) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if invoices.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('invoices', page=invoices.next_num, search=search) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد فواتير</h5>
            <p class="text-muted">لم يتم العثور على فواتير تطابق معايير البحث</p>
            <a href="{{ url_for('pos') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء فاتورة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set today's date as default for date inputs
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (!startDate.value) {
        // Set start date to beginning of current month
        const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
        startDate.value = firstDay.toISOString().split('T')[0];
    }
    
    if (!endDate.value) {
        endDate.value = today;
    }
});
</script>
{% endblock %}
