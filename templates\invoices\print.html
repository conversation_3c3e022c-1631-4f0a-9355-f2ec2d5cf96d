<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ invoice.invoice_number }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            max-width: 80mm;
            margin: 0 auto;
            padding: 10px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .company-info {
            font-size: 12px;
            color: #666;
        }
        
        .invoice-info {
            margin-bottom: 15px;
        }
        
        .invoice-info table {
            width: 100%;
            font-size: 12px;
        }
        
        .invoice-info td {
            padding: 2px 0;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .items-table th,
        .items-table td {
            padding: 5px 2px;
            text-align: right;
            border-bottom: 1px solid #ddd;
            font-size: 12px;
        }
        
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .totals {
            border-top: 2px solid #333;
            padding-top: 10px;
        }
        
        .totals table {
            width: 100%;
            font-size: 12px;
        }
        
        .totals .total-row {
            font-weight: bold;
            font-size: 14px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            font-size: 11px;
            color: #666;
        }
        
        .print-btn {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .barcode {
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <button class="print-btn no-print" onclick="window.print()">
        <i class="fas fa-print"></i> طباعة
    </button>
    
    <!-- Header -->
    <div class="header">
        <div class="company-name">متجر الأمل</div>
        <div class="company-info">
            الرياض، المملكة العربية السعودية<br>
            هاتف: +966-11-123-4567<br>
            الرقم الضريبي: *********
        </div>
    </div>
    
    <!-- Invoice Info -->
    <div class="invoice-info">
        <table>
            <tr>
                <td><strong>رقم الفاتورة:</strong></td>
                <td>{{ invoice.invoice_number }}</td>
            </tr>
            <tr>
                <td><strong>التاريخ:</strong></td>
                <td>{{ invoice.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
            </tr>
            <tr>
                <td><strong>الكاشير:</strong></td>
                <td>{{ invoice.user.full_name }}</td>
            </tr>
            {% if invoice.customer %}
            <tr>
                <td><strong>العميل:</strong></td>
                <td>{{ invoice.customer.name }}</td>
            </tr>
            {% if invoice.customer.phone %}
            <tr>
                <td><strong>الجوال:</strong></td>
                <td>{{ invoice.customer.phone }}</td>
            </tr>
            {% endif %}
            {% endif %}
            <tr>
                <td><strong>طريقة الدفع:</strong></td>
                <td>
                    {% if invoice.payment_method == 'cash' %}نقدي
                    {% elif invoice.payment_method == 'card' %}بطاقة
                    {% else %}آجل{% endif %}
                </td>
            </tr>
        </table>
    </div>
    
    <!-- Items -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 40%">المنتج</th>
                <th style="width: 15%">السعر</th>
                <th style="width: 15%">الكمية</th>
                <th style="width: 20%">الإجمالي</th>
            </tr>
        </thead>
        <tbody>
            {% for item in invoice.items %}
            <tr>
                <td>
                    {{ item.product.name }}
                    {% if item.product.barcode %}
                    <br><small style="color: #666;">{{ item.product.barcode }}</small>
                    {% endif %}
                </td>
                <td>{{ "%.2f"|format(item.unit_price) }}</td>
                <td>{{ item.quantity }}</td>
                <td><strong>{{ "%.2f"|format(item.total_price) }}</strong></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <!-- Totals -->
    <div class="totals">
        <table>
            <tr>
                <td>المجموع الفرعي:</td>
                <td style="text-align: left;">{{ "%.2f"|format(invoice.subtotal) }} ر.س</td>
            </tr>
            {% if invoice.discount_amount > 0 %}
            <tr>
                <td>الخصم:</td>
                <td style="text-align: left; color: red;">-{{ "%.2f"|format(invoice.discount_amount) }} ر.س</td>
            </tr>
            {% endif %}
            <tr>
                <td>الضريبة (15%):</td>
                <td style="text-align: left;">{{ "%.2f"|format(invoice.tax_amount) }} ر.س</td>
            </tr>
            <tr class="total-row">
                <td><strong>الإجمالي:</strong></td>
                <td style="text-align: left;"><strong>{{ "%.2f"|format(invoice.total_amount) }} ر.س</strong></td>
            </tr>
        </table>
    </div>
    
    <!-- Barcode -->
    <div class="barcode">
        <div>||||| |||| | ||| |||| |||||</div>
        <div>{{ invoice.invoice_number }}</div>
    </div>
    
    <!-- Footer -->
    <div class="footer">
        شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى<br>
        تم إنشاء هذه الفاتورة إلكترونياً
    </div>
    
    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Print shortcut
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
