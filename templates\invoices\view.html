{% extends "base.html" %}

{% block title %}فاتورة رقم {{ invoice.invoice_number }} - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-invoice me-2"></i>
        فاتورة رقم {{ invoice.invoice_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('print_invoice', invoice_id=invoice.id) }}" 
               class="btn btn-primary" target="_blank">
                <i class="fas fa-print me-1"></i>
                طباعة
            </a>
            <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للفواتير
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Invoice Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">تفاصيل الفاتورة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الفاتورة:</strong></td>
                                <td>{{ invoice.invoice_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>{{ invoice.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>الكاشير:</strong></td>
                                <td>{{ invoice.user.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>طريقة الدفع:</strong></td>
                                <td>
                                    {% if invoice.payment_method == 'cash' %}
                                        <span class="badge bg-success">نقدي</span>
                                    {% elif invoice.payment_method == 'card' %}
                                        <span class="badge bg-info">بطاقة</span>
                                    {% else %}
                                        <span class="badge bg-warning">آجل</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>العميل:</strong></td>
                                <td>
                                    {% if invoice.customer %}
                                        {{ invoice.customer.name }}
                                        {% if invoice.customer.phone %}
                                            <br><small class="text-muted">{{ invoice.customer.phone }}</small>
                                        {% endif %}
                                    {% else %}
                                        عميل نقدي
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حالة الدفع:</strong></td>
                                <td>
                                    {% if invoice.payment_status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                    {% elif invoice.payment_status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% else %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if invoice.notes %}
                            <tr>
                                <td><strong>ملاحظات:</strong></td>
                                <td>{{ invoice.notes }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Invoice Items -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">عناصر الفاتورة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice.items %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ item.product.name }}</strong>
                                    {% if item.product.barcode %}
                                        <br><small class="text-muted">{{ item.product.barcode }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(item.unit_price) }} ر.س</td>
                                <td>{{ item.quantity }}</td>
                                <td><strong>{{ "%.2f"|format(item.total_price) }} ر.س</strong></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Invoice Summary -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">ملخص الفاتورة</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td>المجموع الفرعي:</td>
                        <td class="text-end">{{ "%.2f"|format(invoice.subtotal) }} ر.س</td>
                    </tr>
                    {% if invoice.discount_amount > 0 %}
                    <tr>
                        <td>الخصم:</td>
                        <td class="text-end text-danger">-{{ "%.2f"|format(invoice.discount_amount) }} ر.س</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td>الضريبة (15%):</td>
                        <td class="text-end">{{ "%.2f"|format(invoice.tax_amount) }} ر.س</td>
                    </tr>
                    <tr class="border-top">
                        <td><strong>الإجمالي:</strong></td>
                        <td class="text-end"><strong class="text-success fs-5">{{ "%.2f"|format(invoice.total_amount) }} ر.س</strong></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('print_invoice', invoice_id=invoice.id) }}" 
                       class="btn btn-primary" target="_blank">
                        <i class="fas fa-print me-2"></i>
                        طباعة الفاتورة
                    </a>
                    
                    {% if current_user.role in ['admin', 'manager'] %}
                    <button class="btn btn-outline-warning" onclick="editInvoice()">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الفاتورة
                    </button>
                    
                    {% if invoice.payment_status != 'cancelled' %}
                    <button class="btn btn-outline-danger" onclick="cancelInvoice()">
                        <i class="fas fa-times me-2"></i>
                        إلغاء الفاتورة
                    </button>
                    {% endif %}
                    {% endif %}
                    
                    <a href="{{ url_for('pos') }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        فاتورة جديدة
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Invoice Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">إحصائيات</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-primary">{{ invoice.items|length }}</h5>
                        <small class="text-muted">عدد الأصناف</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-info">{{ invoice.items|sum(attribute='quantity') }}</h5>
                        <small class="text-muted">إجمالي الكمية</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editInvoice() {
    alert('ميزة تعديل الفاتورة قيد التطوير');
    // Here you would implement invoice editing functionality
}

function cancelInvoice() {
    if (confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟\nسيتم إرجاع الكميات إلى المخزون.')) {
        // Here you would implement invoice cancellation
        alert('تم إلغاء الفاتورة بنجاح');
        location.reload();
    }
}

// Print shortcut
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        window.open('{{ url_for("print_invoice", invoice_id=invoice.id) }}', '_blank');
    }
});
</script>
{% endblock %}
