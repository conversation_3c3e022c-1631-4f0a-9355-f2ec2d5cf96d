{% extends "base.html" %}

{% block title %}نقطة البيع - نظام نقاط البيع{% endblock %}

{% block extra_css %}
<style>
    .pos-container {
        height: calc(100vh - 100px);
    }
    
    .product-grid {
        max-height: 60vh;
        overflow-y: auto;
    }
    
    .product-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        border-color: #667eea;
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    
    .cart-container {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        height: 100%;
    }
    
    .cart-items {
        max-height: 40vh;
        overflow-y: auto;
    }
    
    .cart-item {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
    }
    
    .total-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .search-section {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    .category-btn {
        margin: 5px;
        border-radius: 20px;
        padding: 8px 16px;
        border: 2px solid #667eea;
        background: white;
        color: #667eea;
        transition: all 0.3s ease;
    }
    
    .category-btn.active,
    .category-btn:hover {
        background: #667eea;
        color: white;
    }
    
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .quantity-btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: none;
        background: #667eea;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    .quantity-input {
        width: 60px;
        text-align: center;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="pos-container">
    <div class="row h-100">
        <!-- Products Section -->
        <div class="col-lg-8">
            <!-- Search and Categories -->
            <div class="search-section">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" id="searchProduct" class="form-control" 
                                   placeholder="البحث عن منتج أو مسح الباركود...">
                            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-end">
                            <button class="category-btn active" data-category="all">الكل</button>
                            {% for category in categories %}
                            <button class="category-btn" data-category="{{ category.id }}">
                                {{ category.name }}
                            </button>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="product-grid">
                <div class="row" id="productsContainer">
                    {% for product in products %}
                    <div class="col-xl-3 col-lg-4 col-md-6 mb-3 product-item" 
                         data-category="{{ product.category_id or 'none' }}"
                         data-product-id="{{ product.id }}"
                         data-product-name="{{ product.name }}"
                         data-product-price="{{ product.selling_price }}"
                         data-product-stock="{{ product.stock_quantity }}">
                        <div class="card product-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-2">
                                    {% if product.image_path %}
                                    <img src="{{ product.image_path }}" class="img-fluid" 
                                         style="max-height: 80px;" alt="{{ product.name }}">
                                    {% else %}
                                    <i class="fas fa-box fa-3x text-muted"></i>
                                    {% endif %}
                                </div>
                                <h6 class="card-title">{{ product.name }}</h6>
                                <p class="card-text">
                                    <strong class="text-success">{{ "%.2f"|format(product.selling_price) }} ر.س</strong><br>
                                    <small class="text-muted">المخزون: {{ product.stock_quantity }}</small>
                                </p>
                                {% if product.stock_quantity > 0 %}
                                <button class="btn btn-primary btn-sm add-to-cart">
                                    <i class="fas fa-plus"></i> إضافة
                                </button>
                                {% else %}
                                <button class="btn btn-secondary btn-sm" disabled>
                                    نفد المخزون
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <!-- Cart Section -->
        <div class="col-lg-4">
            <div class="cart-container">
                <h5 class="mb-3">
                    <i class="fas fa-shopping-cart me-2"></i>
                    سلة المشتريات
                </h5>
                
                <!-- Customer Selection -->
                <div class="mb-3">
                    <label class="form-label">العميل (اختياري)</label>
                    <select class="form-select" id="customerSelect">
                        <option value="">عميل نقدي</option>
                        {% for customer in customers %}
                        <option value="{{ customer.id }}">{{ customer.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Cart Items -->
                <div class="cart-items" id="cartItems">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>السلة فارغة</p>
                    </div>
                </div>
                
                <!-- Total Section -->
                <div class="total-section">
                    <div class="row mb-2">
                        <div class="col">المجموع الفرعي:</div>
                        <div class="col text-end" id="subtotal">0.00 ر.س</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">الضريبة (15%):</div>
                        <div class="col text-end" id="taxAmount">0.00 ر.س</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">الخصم:</div>
                        <div class="col text-end">
                            <input type="number" class="form-control form-control-sm text-end" 
                                   id="discountAmount" value="0" min="0" step="0.01">
                        </div>
                    </div>
                    <hr>
                    <div class="row mb-3">
                        <div class="col"><h5>الإجمالي:</h5></div>
                        <div class="col text-end"><h5 id="totalAmount">0.00 ر.س</h5></div>
                    </div>
                    
                    <!-- Payment Method -->
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="paymentMethod">
                            <option value="cash">نقدي</option>
                            <option value="card">بطاقة</option>
                            <option value="credit">آجل</option>
                        </select>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-light btn-lg" id="processPayment" disabled>
                            <i class="fas fa-credit-card me-2"></i>
                            إتمام البيع
                        </button>
                        <button class="btn btn-outline-light" id="clearCart">
                            <i class="fas fa-trash me-2"></i>
                            مسح السلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Modal -->
<div class="modal fade" id="invoiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">فاتورة البيع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="invoiceContent">
                <!-- Invoice content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="printInvoice">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
                <button type="button" class="btn btn-success" id="newSale">
                    <i class="fas fa-plus me-2"></i>
                    بيع جديد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let cart = [];
const TAX_RATE = 0.15;

// Initialize POS
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateCartDisplay();
});

function initializeEventListeners() {
    // Add to cart buttons
    document.querySelectorAll('.add-to-cart').forEach(btn => {
        btn.addEventListener('click', function() {
            const productCard = this.closest('.product-item');
            addToCart({
                id: parseInt(productCard.dataset.productId),
                name: productCard.dataset.productName,
                price: parseFloat(productCard.dataset.productPrice),
                stock: parseInt(productCard.dataset.productStock)
            });
        });
    });

    // Category filter
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            filterProducts(this.dataset.category);
        });
    });

    // Search functionality
    document.getElementById('searchProduct').addEventListener('input', function() {
        searchProducts(this.value);
    });

    // Barcode scanning (Enter key)
    document.getElementById('searchProduct').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchByBarcode(this.value);
        }
    });

    // Discount calculation
    document.getElementById('discountAmount').addEventListener('input', updateTotals);

    // Process payment
    document.getElementById('processPayment').addEventListener('click', processPayment);

    // Clear cart
    document.getElementById('clearCart').addEventListener('click', clearCart);

    // New sale
    document.getElementById('newSale').addEventListener('click', function() {
        clearCart();
        $('#invoiceModal').modal('hide');
    });

    // Print invoice
    document.getElementById('printInvoice').addEventListener('click', function() {
        const invoiceId = this.dataset.invoiceId;
        if (invoiceId) {
            window.open(`/invoice/${invoiceId}/print`, '_blank');
        }
    });
}

function addToCart(product) {
    const existingItem = cart.find(item => item.id === product.id);

    if (existingItem) {
        if (existingItem.quantity < product.stock) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
            return;
        }
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: 1,
            total: product.price,
            stock: product.stock
        });
    }

    updateCartDisplay();
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
}

function updateQuantity(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        if (newQuantity <= 0) {
            removeFromCart(productId);
        } else if (newQuantity <= item.stock) {
            item.quantity = newQuantity;
            item.total = item.quantity * item.price;
            updateCartDisplay();
        } else {
            alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
        }
    }
}

function updateCartDisplay() {
    const cartContainer = document.getElementById('cartItems');

    if (cart.length === 0) {
        cartContainer.innerHTML = \`
            <div class="text-center text-muted py-4">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>السلة فارغة</p>
            </div>
        \`;
        document.getElementById('processPayment').disabled = true;
    } else {
        cartContainer.innerHTML = cart.map(item => \`
            <div class="cart-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">\${item.name}</h6>
                        <small class="text-muted">\${item.price.toFixed(2)} ر.س × \${item.quantity}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(\${item.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(\${item.id}, \${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" value="\${item.quantity}"
                               min="1" max="\${item.stock}"
                               onchange="updateQuantity(\${item.id}, parseInt(this.value))">
                        <button class="quantity-btn" onclick="updateQuantity(\${item.id}, \${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <strong class="text-success">\${item.total.toFixed(2)} ر.س</strong>
                </div>
            </div>
        \`).join('');

        document.getElementById('processPayment').disabled = false;
    }

    updateTotals();
}

function updateTotals() {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * TAX_RATE;
    const total = taxableAmount + taxAmount;

    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
    document.getElementById('taxAmount').textContent = taxAmount.toFixed(2) + ' ر.س';
    document.getElementById('totalAmount').textContent = total.toFixed(2) + ' ر.س';
}

function filterProducts(categoryId) {
    const products = document.querySelectorAll('.product-item');
    products.forEach(product => {
        if (categoryId === 'all' || product.dataset.category === categoryId) {
            product.style.display = 'block';
        } else {
            product.style.display = 'none';
        }
    });
}

function searchProducts(query) {
    const products = document.querySelectorAll('.product-item');
    products.forEach(product => {
        const productName = product.dataset.productName.toLowerCase();
        if (productName.includes(query.toLowerCase())) {
            product.style.display = 'block';
        } else {
            product.style.display = 'none';
        }
    });
}

function searchByBarcode(barcode) {
    if (!barcode.trim()) return;

    fetch(\`/api/search_product?barcode=\${encodeURIComponent(barcode)}\`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.product) {
                addToCart(data.product);
                document.getElementById('searchProduct').value = '';
            } else {
                alert('لم يتم العثور على منتج بهذا الباركود');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في البحث');
        });
}

function clearCart() {
    cart = [];
    updateCartDisplay();
    document.getElementById('discountAmount').value = 0;
    document.getElementById('customerSelect').value = '';
    document.getElementById('paymentMethod').value = 'cash';
}

function processPayment() {
    if (cart.length === 0) {
        alert('السلة فارغة');
        return;
    }

    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * TAX_RATE;
    const total = taxableAmount + taxAmount;

    const invoiceData = {
        customer_id: document.getElementById('customerSelect').value || null,
        payment_method: document.getElementById('paymentMethod').value,
        subtotal: subtotal,
        tax_amount: taxAmount,
        discount_amount: discountAmount,
        total_amount: total,
        items: cart.map(item => ({
            product_id: item.id,
            quantity: item.quantity,
            unit_price: item.price,
            total_price: item.total
        }))
    };

    // Show loading
    document.getElementById('processPayment').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
    document.getElementById('processPayment').disabled = true;

    fetch('/api/create_invoice', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Load invoice content
            fetch(\`/invoice/\${data.invoice_id}\`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('invoiceContent').innerHTML = html;
                    document.getElementById('printInvoice').dataset.invoiceId = data.invoice_id;
                    \$('#invoiceModal').modal('show');
                });
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في معالجة الطلب');
    })
    .finally(() => {
        document.getElementById('processPayment').innerHTML = '<i class="fas fa-credit-card me-2"></i>إتمام البيع';
        document.getElementById('processPayment').disabled = false;
    });
}
</script>
{% endblock %}
