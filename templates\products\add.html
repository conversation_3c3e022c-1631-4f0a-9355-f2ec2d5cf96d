{% extends "base.html" %}

{% block title %}إضافة منتج جديد - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>
        إضافة منتج جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للمنتجات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات المنتج</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="barcode" class="form-label">الباركود</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="barcode" name="barcode">
                                <button class="btn btn-outline-secondary" type="button" onclick="generateBarcode()">
                                    <i class="fas fa-magic"></i> توليد
                                </button>
                            </div>
                            <small class="form-text text-muted">اتركه فارغاً للتوليد التلقائي</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">التصنيف</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">اختر التصنيف</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="image" class="form-label">صورة المنتج</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="purchase_price" class="form-label">سعر الشراء <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="purchase_price" name="purchase_price" 
                                       step="0.01" min="0" required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="selling_price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                       step="0.01" min="0" required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">الكمية الحالية <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                   min="0" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="min_stock_level" class="form-label">الحد الأدنى للمخزون</label>
                            <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" 
                                   value="5" min="0">
                            <small class="form-text text-muted">سيتم التنبيه عند الوصول لهذا الحد</small>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح مفيدة:</h6>
                    <ul class="mb-0">
                        <li>استخدم أسماء واضحة ومميزة للمنتجات</li>
                        <li>أضف وصفاً مفصلاً لتسهيل البحث</li>
                        <li>تأكد من دقة أسعار الشراء والبيع</li>
                        <li>حدد الحد الأدنى للمخزون لتجنب نفاد المنتج</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                    <p class="mb-0">تأكد من صحة البيانات قبل الحفظ، حيث أن بعض البيانات قد تؤثر على العمليات المحاسبية.</p>
                </div>
            </div>
        </div>
        
        <!-- Profit Calculation -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    حساب الربح
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-2">
                        <label class="form-label">الربح المتوقع:</label>
                        <h4 class="text-success" id="profitAmount">0.00 ر.س</h4>
                    </div>
                    <div class="col-12">
                        <label class="form-label">نسبة الربح:</label>
                        <h5 class="text-info" id="profitPercentage">0%</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate profit when prices change
    const purchasePrice = document.getElementById('purchase_price');
    const sellingPrice = document.getElementById('selling_price');
    
    purchasePrice.addEventListener('input', calculateProfit);
    sellingPrice.addEventListener('input', calculateProfit);
    
    // Auto-calculate selling price with 30% markup
    purchasePrice.addEventListener('blur', function() {
        if (this.value && !sellingPrice.value) {
            const markup = parseFloat(this.value) * 1.3;
            sellingPrice.value = markup.toFixed(2);
            calculateProfit();
        }
    });
});

function calculateProfit() {
    const purchase = parseFloat(document.getElementById('purchase_price').value) || 0;
    const selling = parseFloat(document.getElementById('selling_price').value) || 0;
    
    const profit = selling - purchase;
    const profitPercentage = purchase > 0 ? ((profit / purchase) * 100) : 0;
    
    document.getElementById('profitAmount').textContent = profit.toFixed(2) + ' ر.س';
    document.getElementById('profitPercentage').textContent = profitPercentage.toFixed(1) + '%';
    
    // Change color based on profit
    const profitElement = document.getElementById('profitAmount');
    const percentageElement = document.getElementById('profitPercentage');
    
    if (profit > 0) {
        profitElement.className = 'text-success';
        percentageElement.className = 'text-success';
    } else if (profit < 0) {
        profitElement.className = 'text-danger';
        percentageElement.className = 'text-danger';
    } else {
        profitElement.className = 'text-muted';
        percentageElement.className = 'text-muted';
    }
}

function generateBarcode() {
    // Generate a simple barcode (timestamp-based)
    const timestamp = Date.now().toString();
    const barcode = timestamp.slice(-10); // Last 10 digits
    document.getElementById('barcode').value = barcode;
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value);
    const sellingPrice = parseFloat(document.getElementById('selling_price').value);
    const stockQuantity = parseInt(document.getElementById('stock_quantity').value);
    
    if (!name) {
        alert('يرجى إدخال اسم المنتج');
        e.preventDefault();
        return;
    }
    
    if (purchasePrice < 0 || sellingPrice < 0 || stockQuantity < 0) {
        alert('لا يمكن أن تكون القيم سالبة');
        e.preventDefault();
        return;
    }
    
    if (sellingPrice < purchasePrice) {
        if (!confirm('سعر البيع أقل من سعر الشراء. هل تريد المتابعة؟')) {
            e.preventDefault();
            return;
        }
    }
});
</script>
{% endblock %}
