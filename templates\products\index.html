{% extends "base.html" %}

{% block title %}إدارة المنتجات - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-box me-2"></i>
        إدارة المنتجات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.role in ['admin', 'manager'] %}
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة منتج جديد
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="اسم المنتج...">
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">التصنيف</label>
                <select class="form-select" id="category" name="category">
                    <option value="">جميع التصنيفات</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" 
                            {% if selected_category == category.id %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المنتجات</h5>
    </div>
    <div class="card-body">
        {% if products.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>اسم المنتج</th>
                        <th>التصنيف</th>
                        <th>الباركود</th>
                        <th>سعر الشراء</th>
                        <th>سعر البيع</th>
                        <th>المخزون</th>
                        <th>الحالة</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products.items %}
                    <tr>
                        <td>
                            {% if product.image_path %}
                            <img src="{{ product.image_path }}" class="img-thumbnail" 
                                 style="width: 50px; height: 50px;" alt="{{ product.name }}">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                 style="width: 50px; height: 50px; border-radius: 5px;">
                                <i class="fas fa-box text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.description %}
                            <br><small class="text-muted">{{ product.description[:50] }}...</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.category %}
                                <span class="badge bg-info">{{ product.category.name }}</span>
                            {% else %}
                                <span class="badge bg-secondary">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.barcode %}
                                <code>{{ product.barcode }}</code>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>{{ "%.2f"|format(product.purchase_price) }} ر.س</td>
                        <td><strong class="text-success">{{ "%.2f"|format(product.selling_price) }} ر.س</strong></td>
                        <td>
                            {% if product.is_low_stock() %}
                                <span class="badge bg-warning">{{ product.stock_quantity }}</span>
                                <small class="text-danger d-block">مخزون منخفض</small>
                            {% else %}
                                <span class="badge bg-success">{{ product.stock_quantity }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                {% if current_user.role in ['admin', 'manager'] %}
                                <a href="{{ url_for('edit_product', product_id=product.id) }}" 
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                <button class="btn btn-outline-info" title="عرض التفاصيل"
                                        onclick="showProductDetails({{ product.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if products.pages > 1 %}
        <nav aria-label="Product pagination">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('products', page=products.prev_num, search=search, category=selected_category) }}">
                        السابق
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=page_num, search=search, category=selected_category) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('products', page=products.next_num, search=search, category=selected_category) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-box fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات</h5>
            <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
            {% if current_user.role in ['admin', 'manager'] %}
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Product Details Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productDetails">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showProductDetails(productId) {
    // This would fetch and display product details
    // For now, just show a placeholder
    document.getElementById('productDetails').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">جاري تحميل التفاصيل...</p>
        </div>
    `;
    
    $('#productModal').modal('show');
    
    // Here you would typically fetch product details via AJAX
    setTimeout(() => {
        document.getElementById('productDetails').innerHTML = `
            <p>تفاصيل المنتج رقم ${productId} ستظهر هنا</p>
            <p>يمكن إضافة المزيد من التفاصيل مثل:</p>
            <ul>
                <li>تاريخ الإضافة</li>
                <li>آخر تحديث</li>
                <li>سجل حركات المخزون</li>
                <li>إحصائيات المبيعات</li>
            </ul>
        `;
    }, 1000);
}
</script>
{% endblock %}
