{% extends "base.html" %}

{% block title %}التقارير - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar me-2"></i>
        التقارير والإحصائيات
    </h1>
</div>

<div class="row">
    <!-- Sales Report -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    تقرير المبيعات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">عرض تقارير المبيعات حسب الفترة الزمنية مع إمكانية التصفية والتصدير.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>المبيعات اليومية والشهرية</li>
                    <li><i class="fas fa-check text-success me-2"></i>تصفية حسب التاريخ</li>
                    <li><i class="fas fa-check text-success me-2"></i>تصدير إلى Excel/PDF</li>
                    <li><i class="fas fa-check text-success me-2"></i>إحصائيات مفصلة</li>
                </ul>
                <a href="{{ url_for('sales_report') }}" class="btn btn-primary">
                    <i class="fas fa-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>
    
    <!-- Products Report -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    تقرير المنتجات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">تقارير شاملة عن المنتجات والمخزون والمنتجات الأكثر مبيعاً.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>المنتجات الأكثر مبيعاً</li>
                    <li><i class="fas fa-check text-success me-2"></i>تقرير المخزون</li>
                    <li><i class="fas fa-check text-success me-2"></i>المنتجات منخفضة المخزون</li>
                    <li><i class="fas fa-check text-success me-2"></i>تحليل الأرباح</li>
                </ul>
                <a href="{{ url_for('products_report') }}" class="btn btn-success">
                    <i class="fas fa-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>
    
    <!-- Customers Report -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    تقرير العملاء
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">تقارير العملاء ومشترياتهم والعملاء الأكثر شراءً.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>العملاء الأكثر شراءً</li>
                    <li><i class="fas fa-check text-success me-2"></i>تقرير المديونيات</li>
                    <li><i class="fas fa-check text-success me-2"></i>إحصائيات العملاء</li>
                    <li><i class="fas fa-check text-success me-2"></i>سجل المشتريات</li>
                </ul>
                <a href="{{ url_for('customers_report') }}" class="btn btn-info">
                    <i class="fas fa-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>
    
    <!-- Financial Report -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-dollar-sign me-2"></i>
                    التقرير المالي
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">تقارير الأرباح والخسائر والتحليل المالي الشامل.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>الأرباح والخسائر</li>
                    <li><i class="fas fa-check text-success me-2"></i>التدفق النقدي</li>
                    <li><i class="fas fa-check text-success me-2"></i>تحليل الإيرادات</li>
                    <li><i class="fas fa-check text-success me-2"></i>مقارنة الفترات</li>
                </ul>
                <a href="{{ url_for('financial_report') }}" class="btn btn-warning">
                    <i class="fas fa-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="border rounded p-3">
                            <h3 class="text-primary" id="todaySales">0.00</h3>
                            <p class="mb-0">مبيعات اليوم (ر.س)</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="border rounded p-3">
                            <h3 class="text-success" id="monthSales">0.00</h3>
                            <p class="mb-0">مبيعات الشهر (ر.س)</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="border rounded p-3">
                            <h3 class="text-info" id="totalProducts">0</h3>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="border rounded p-3">
                            <h3 class="text-warning" id="totalCustomers">0</h3>
                            <p class="mb-0">إجمالي العملاء</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    خيارات التصدير
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>تصدير البيانات</h6>
                        <p class="text-muted">تصدير جميع البيانات إلى ملفات مختلفة</p>
                        <div class="btn-group">
                            <button class="btn btn-outline-success" onclick="exportData('excel')">
                                <i class="fas fa-file-excel me-2"></i>
                                Excel
                            </button>
                            <button class="btn btn-outline-danger" onclick="exportData('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-primary" onclick="exportData('csv')">
                                <i class="fas fa-file-csv me-2"></i>
                                CSV
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>النسخ الاحتياطي</h6>
                        <p class="text-muted">إنشاء نسخة احتياطية من قاعدة البيانات</p>
                        <button class="btn btn-outline-secondary" onclick="createBackup()">
                            <i class="fas fa-database me-2"></i>
                            إنشاء نسخة احتياطية
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadQuickStats();
});

function loadQuickStats() {
    // This would typically fetch data from the server
    // For now, we'll use placeholder data
    document.getElementById('todaySales').textContent = '1,250.00';
    document.getElementById('monthSales').textContent = '45,680.00';
    document.getElementById('totalProducts').textContent = '156';
    document.getElementById('totalCustomers').textContent = '89';
}

function exportData(format) {
    alert(`سيتم تصدير البيانات بصيغة ${format.toUpperCase()}`);
    // Here you would implement the actual export functionality
}

function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        alert('تم إنشاء النسخة الاحتياطية بنجاح');
        // Here you would implement the backup functionality
    }
}
</script>
{% endblock %}
