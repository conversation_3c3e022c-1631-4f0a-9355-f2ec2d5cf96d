{% extends "base.html" %}

{% block title %}تقرير المبيعات - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-line me-2"></i>
        تقرير المبيعات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button class="btn btn-outline-success" onclick="exportReport('excel')">
                <i class="fas fa-file-excel me-1"></i>
                Excel
            </button>
            <button class="btn btn-outline-danger" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf me-1"></i>
                PDF
            </button>
        </div>
        <a href="{{ url_for('reports') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للتقارير
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">تصفية التقرير</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ start_date }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ end_date }}">
            </div>
            <div class="col-md-3">
                <label for="payment_method" class="form-label">طريقة الدفع</label>
                <select class="form-select" id="payment_method" name="payment_method">
                    <option value="">جميع الطرق</option>
                    <option value="cash">نقدي</option>
                    <option value="card">بطاقة</option>
                    <option value="credit">آجل</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        تطبيق التصفية
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4>{{ "%.2f"|format(total_sales) }}</h4>
                        <p class="mb-0">إجمالي المبيعات (ر.س)</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4>{{ total_invoices }}</h4>
                        <p class="mb-0">عدد الفواتير</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4>{{ "%.2f"|format(total_sales / total_invoices if total_invoices > 0 else 0) }}</h4>
                        <p class="mb-0">متوسط الفاتورة (ر.س)</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-bar fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4>{{ invoices|sum(attribute='items')|sum(attribute='quantity') if invoices else 0 }}</h4>
                        <p class="mb-0">إجمالي الكمية المباعة</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-boxes fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Chart -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">رسم بياني للمبيعات</h5>
    </div>
    <div class="card-body">
        <canvas id="salesChart" width="400" height="100"></canvas>
    </div>
</div>

<!-- Detailed Sales Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تفاصيل المبيعات</h5>
    </div>
    <div class="card-body">
        {% if invoices %}
        <div class="table-responsive">
            <table class="table table-hover" id="salesTable">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>الكاشير</th>
                        <th>عدد الأصناف</th>
                        <th>المبلغ الفرعي</th>
                        <th>الضريبة</th>
                        <th>الخصم</th>
                        <th>الإجمالي</th>
                        <th>طريقة الدفع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>
                            <a href="{{ url_for('view_invoice', invoice_id=invoice.id) }}" 
                               class="text-decoration-none">
                                {{ invoice.invoice_number }}
                            </a>
                        </td>
                        <td>{{ invoice.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            {% if invoice.customer %}
                                {{ invoice.customer.name }}
                            {% else %}
                                عميل نقدي
                            {% endif %}
                        </td>
                        <td>{{ invoice.user.full_name }}</td>
                        <td>{{ invoice.items|length }}</td>
                        <td>{{ "%.2f"|format(invoice.subtotal) }}</td>
                        <td>{{ "%.2f"|format(invoice.tax_amount) }}</td>
                        <td>{{ "%.2f"|format(invoice.discount_amount) }}</td>
                        <td><strong>{{ "%.2f"|format(invoice.total_amount) }}</strong></td>
                        <td>
                            {% if invoice.payment_method == 'cash' %}
                                <span class="badge bg-success">نقدي</span>
                            {% elif invoice.payment_method == 'card' %}
                                <span class="badge bg-info">بطاقة</span>
                            {% else %}
                                <span class="badge bg-warning">آجل</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-dark">
                        <th colspan="5">الإجمالي</th>
                        <th>{{ "%.2f"|format(invoices|sum(attribute='subtotal')) }}</th>
                        <th>{{ "%.2f"|format(invoices|sum(attribute='tax_amount')) }}</th>
                        <th>{{ "%.2f"|format(invoices|sum(attribute='discount_amount')) }}</th>
                        <th>{{ "%.2f"|format(invoices|sum(attribute='total_amount')) }}</th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مبيعات</h5>
            <p class="text-muted">لم يتم العثور على مبيعات في الفترة المحددة</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    
    if (!document.getElementById('start_date').value) {
        document.getElementById('start_date').value = firstDay;
    }
    if (!document.getElementById('end_date').value) {
        document.getElementById('end_date').value = today;
    }
    
    // Initialize chart
    initSalesChart();
});

function initSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    
    // Sample data - in real implementation, this would come from the server
    const salesData = {
        labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
        datasets: [{
            label: 'المبيعات (ر.س)',
            data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    };
    
    new Chart(ctx, {
        type: 'line',
        data: salesData,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'مبيعات الأسبوع'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function exportReport(format) {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const paymentMethod = document.getElementById('payment_method').value;
    
    const params = new URLSearchParams({
        format: format,
        start_date: startDate,
        end_date: endDate,
        payment_method: paymentMethod
    });
    
    window.open(`/reports/sales/export?${params.toString()}`, '_blank');
}
</script>
{% endblock %}
