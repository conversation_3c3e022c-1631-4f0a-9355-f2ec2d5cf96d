{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام نقاط البيع{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>
        إعدادات النظام
    </h1>
</div>

<div class="row">
    <!-- Company Settings -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    معلومات الشركة
                </h5>
            </div>
            <div class="card-body">
                <form id="companyForm">
                    <div class="mb-3">
                        <label for="company_name" class="form-label">اسم الشركة</label>
                        <input type="text" class="form-control" id="company_name" 
                               value="متجر الأمل" required>
                    </div>
                    <div class="mb-3">
                        <label for="company_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="company_address" rows="3" 
                                  required>الرياض، المملكة العربية السعودية</textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_phone" class="form-label">الهاتف</label>
                            <input type="tel" class="form-control" id="company_phone" 
                                   value="+966-11-123-4567" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="company_email" 
                                   value="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="tax_number" class="form-label">الرقم الضريبي</label>
                        <input type="text" class="form-control" id="tax_number" 
                               value="*********">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ معلومات الشركة
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Tax & Currency Settings -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-percentage me-2"></i>
                    إعدادات الضريبة والعملة
                </h5>
            </div>
            <div class="card-body">
                <form id="taxForm">
                    <div class="mb-3">
                        <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="tax_rate" 
                                   value="15" min="0" max="100" step="0.01" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="currency" class="form-label">العملة</label>
                            <input type="text" class="form-control" id="currency" 
                                   value="ريال سعودي" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="currency_symbol" class="form-label">رمز العملة</label>
                            <input type="text" class="form-control" id="currency_symbol" 
                                   value="ر.س" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_tax" checked>
                            <label class="form-check-label" for="include_tax">
                                تطبيق الضريبة على جميع المبيعات
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>
                        حفظ إعدادات الضريبة
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Receipt Settings -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    إعدادات الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <form id="receiptForm">
                    <div class="mb-3">
                        <label for="receipt_header" class="form-label">رأس الفاتورة</label>
                        <textarea class="form-control" id="receipt_header" rows="2" 
                                  placeholder="نص يظهر في أعلى الفاتورة">مرحباً بكم في متجر الأمل</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="receipt_footer" class="form-label">تذييل الفاتورة</label>
                        <textarea class="form-control" id="receipt_footer" rows="2" 
                                  placeholder="نص يظهر في أسفل الفاتورة">شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى</textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="receipt_width" class="form-label">عرض الفاتورة (مم)</label>
                            <select class="form-select" id="receipt_width">
                                <option value="58">58 مم</option>
                                <option value="80" selected>80 مم</option>
                                <option value="110">110 مم</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="receipt_font_size" class="form-label">حجم الخط</label>
                            <select class="form-select" id="receipt_font_size">
                                <option value="10">صغير (10)</option>
                                <option value="12" selected>متوسط (12)</option>
                                <option value="14">كبير (14)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto_print" checked>
                            <label class="form-check-label" for="auto_print">
                                طباعة تلقائية بعد إتمام البيع
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-save me-2"></i>
                        حفظ إعدادات الفاتورة
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- System Settings -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات النظام
                </h5>
            </div>
            <div class="card-body">
                <form id="systemForm">
                    <div class="mb-3">
                        <label for="language" class="form-label">لغة النظام</label>
                        <select class="form-select" id="language">
                            <option value="ar" selected>العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="timezone" class="form-label">المنطقة الزمنية</label>
                        <select class="form-select" id="timezone">
                            <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                            <option value="Asia/Dubai">دبي (GMT+4)</option>
                            <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                        <select class="form-select" id="backup_frequency">
                            <option value="daily" selected>يومي</option>
                            <option value="weekly">أسبوعي</option>
                            <option value="monthly">شهري</option>
                            <option value="manual">يدوي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="low_stock_alerts" checked>
                            <label class="form-check-label" for="low_stock_alerts">
                                تنبيهات المخزون المنخفض
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                            <label class="form-check-label" for="email_notifications">
                                إشعارات البريد الإلكتروني
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>
                        حفظ إعدادات النظام
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- User Management -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    إدارة المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>المستخدمون الحاليون</h6>
                    <button class="btn btn-primary btn-sm" onclick="addUser()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مستخدم
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>الاسم الكامل</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>آخر دخول</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>admin</strong></td>
                                <td>مدير النظام</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-danger">مدير</span></td>
                                <td>{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else 'لم يسجل دخول' }}</td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editUser(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>cashier</strong></td>
                                <td>أحمد الكاشير</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-info">كاشير</span></td>
                                <td>-</td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editUser(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(2)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form submissions
document.getElementById('companyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    saveSettings('company');
});

document.getElementById('taxForm').addEventListener('submit', function(e) {
    e.preventDefault();
    saveSettings('tax');
});

document.getElementById('receiptForm').addEventListener('submit', function(e) {
    e.preventDefault();
    saveSettings('receipt');
});

document.getElementById('systemForm').addEventListener('submit', function(e) {
    e.preventDefault();
    saveSettings('system');
});

function saveSettings(type) {
    // Here you would collect form data and send to server
    alert(`تم حفظ إعدادات ${type} بنجاح`);
}

function addUser() {
    alert('سيتم فتح نموذج إضافة مستخدم جديد');
}

function editUser(userId) {
    alert(`سيتم فتح نموذج تعديل المستخدم رقم ${userId}`);
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        alert(`تم حذف المستخدم رقم ${userId}`);
    }
}
</script>
{% endblock %}
