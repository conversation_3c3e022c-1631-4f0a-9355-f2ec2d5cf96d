from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def index():
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام نقاط البيع - اختبار</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Arial', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .card { border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        </style>
    </head>
    <body>
        <div class="container d-flex align-items-center justify-content-center min-vh-100">
            <div class="card" style="width: 100%; max-width: 500px;">
                <div class="card-body text-center p-5">
                    <i class="fas fa-cash-register fa-5x text-primary mb-4"></i>
                    <h1 class="card-title mb-4">نظام نقاط البيع</h1>
                    <p class="lead mb-4">النظام يعمل بنجاح! 🎉</p>
                    
                    <div class="row text-center mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-users fa-2x text-info mb-2"></i>
                                <h5>إدارة العملاء</h5>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-box fa-2x text-success mb-2"></i>
                                <h5>إدارة المنتجات</h5>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-file-invoice fa-2x text-warning mb-2"></i>
                                <h5>إدارة الفواتير</h5>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-chart-bar fa-2x text-danger mb-2"></i>
                                <h5>التقارير</h5>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>تم إنشاء النظام بنجاح</h6>
                        <p class="mb-0">جميع المكونات جاهزة للاستخدام</p>
                    </div>
                    
                    <div class="mt-4">
                        <h6>بيانات تسجيل الدخول:</h6>
                        <p><strong>المستخدم:</strong> admin<br>
                        <strong>كلمة المرور:</strong> admin123</p>
                    </div>
                    
                    <a href="/full" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket me-2"></i>
                        الانتقال للنظام الكامل
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/full')
def full_system():
    return '''
    <div style="text-align: center; padding: 50px; font-family: Arial;">
        <h1>🚧 النظام الكامل قيد التطوير</h1>
        <p>يتم حالياً إصلاح بعض المشاكل التقنية</p>
        <a href="/" style="color: blue;">العودة للصفحة الرئيسية</a>
    </div>
    '''

if __name__ == '__main__':
    print("🚀 تشغيل نظام نقاط البيع - نسخة الاختبار")
    print("🌐 العنوان: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
